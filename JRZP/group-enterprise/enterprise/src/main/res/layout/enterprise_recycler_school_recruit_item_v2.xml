<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.enterprise.data.SchoolRecruitV2Data" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <ImageView
      android:id="@+id/iv_checked"
      style="@style/wrap_wrap"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12"
      android:src="@drawable/ic_select_selector"
      bind:selected="@{data.checked}" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:layout_width="@dimen/dp_0"
      android:layout_height="wrap_content"
      android:layout_weight="1"
      android:paddingStart="@dimen/dp_12"
      android:paddingEnd="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_12">

      <TextView
        android:id="@+id/tv_title"
        style="@style/Text.16sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        android:ellipsize="end"
        android:lines="1"
        android:text="@{data.title}"
        app:layout_constraintEnd_toStartOf="@id/iv_edit"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <ImageView
        android:id="@+id/iv_edit"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:scaleType="centerInside"
        android:src="@drawable/ic_school_recruit_edit"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toStartOf="@id/iv_delete"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

      <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:scaleType="centerInside"
        android:src="@drawable/ic_school_recruit_delete"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

      <TextView
        android:id="@+id/tv_time"
        style="@style/Text.12sp.888888"
        android:layout_marginTop="@dimen/dp_8"
        android:text="@{data.createTime}"
        app:layout_constraintStart_toStartOf="@id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

      <View
        android:id="@+id/v_divider"
        style="@style/Line.Horizontal.Light"
        android:layout_marginTop="@dimen/dp_8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_time" />

      <TextView
        android:id="@+id/tv_delivery_count"
        style="@style/Text.12sp.333333"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/text_click_underline"
        android:text="@{@string/school_recruit_management_delivery_count(data.toudiCount)}"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_divider" />

      <TextView
        android:id="@+id/tv_review_state"
        style="@style/Text.12sp.333333"
        android:layout_marginStart="@dimen/dp_18"
        android:text="@string/school_recruit_management_review_state"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_delivery_count"
        app:layout_constraintStart_toEndOf="@id/tv_delivery_count" />

      <TextView
        style="@style/Text.12sp"
        android:text="@{data.stateText}"
        android:textColor="@{data.stateTextColor}"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_review_state"
        app:layout_constraintStart_toEndOf="@id/tv_review_state" />
    </androidx.constraintlayout.widget.ConstraintLayout>

  </LinearLayout>
</layout>