package com.bxkj.enterprise.ui.fragment.schooljobfair;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;

import com.bxkj.common.R;
import com.bxkj.common.util.DensityUtils;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.LinePagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.ColorTransitionPagerTitleView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.resume
 * @Description:
 * @TODO: TODO
 * @date 2018/11/12
 */
public class SchoolJobFairTabLayoutAdapter extends CommonNavigatorAdapter {

    private ViewPager mViewPager;
    private String[] mTitles;

    public SchoolJobFairTabLayoutAdapter(ViewPager viewPager) {
        this(viewPager, null);
    }

    public SchoolJobFairTabLayoutAdapter(ViewPager viewPager, String[] titles) {
        mViewPager = viewPager;
        mTitles = titles;
    }

    @Override
    public int getCount() {
        return mViewPager.getAdapter() == null ? 0 : mViewPager.getAdapter().getCount();
    }

    @Override
    public IPagerTitleView getTitleView(Context context, int i) {
        ColorTransitionPagerTitleView pageTitleView = new ColorTransitionPagerTitleView(context);
        pageTitleView.setTextSize(14);
        pageTitleView.setNormalColor(ContextCompat.getColor(context, R.color.cl_333333));
        pageTitleView.setSelectedColor(ContextCompat.getColor(context, R.color.common_white));
        pageTitleView.setText(mTitles == null ? mViewPager.getAdapter().getPageTitle(i) : mTitles[i]);
        pageTitleView.setOnClickListener(view -> mViewPager.setCurrentItem(i));
        return pageTitleView;
    }

    @Override
    public IPagerIndicator getIndicator(Context context) {
        LinePagerIndicator indicator = new LinePagerIndicator(context);
        indicator.setLineHeight(DensityUtils.dp2px(context, 32));
        indicator.setColors(ContextCompat.getColor(context, R.color.common_49C280));
        return indicator;
    }
}
