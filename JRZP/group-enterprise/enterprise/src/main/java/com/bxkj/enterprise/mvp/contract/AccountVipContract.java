package com.bxkj.enterprise.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.jrzp.user.data.AccountVipData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.contract
 * @Description: AccountVip
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface AccountVipContract {
    interface View extends BaseView {
        void getAccountVipInfoSuccess(AccountVipData accountVipData);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getAccountVipInfo(int userId);
    }
}
