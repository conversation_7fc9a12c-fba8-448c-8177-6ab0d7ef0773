package com.bxkj.enterprise.ui.activity.fillintheinfo;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.TextView;
import com.bigkoo.pickerview.builder.OptionsPickerBuilder;
import com.bigkoo.pickerview.view.OptionsPickerView;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.util.picker.OneOptionPicker;
import com.bxkj.common.util.picker.PickerUtils;
import com.bxkj.common.util.router.EnterpriseCommonRouter;
import com.bxkj.common.widget.popup.OptionsPickerPopup;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.ecommon.util.map.LngLat;
import com.bxkj.ecommon.widget.ClearEditText;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.EnterpriseInfoData;
import com.bxkj.enterprise.mvp.contract.EnterpriseInfoContract;
import com.bxkj.enterprise.mvp.contract.IndustryContract;
import com.bxkj.enterprise.mvp.contract.NatureOfCompanyContract;
import com.bxkj.enterprise.mvp.presenter.EnterpriseInfoPresenter;
import com.bxkj.enterprise.mvp.presenter.IndustryPresenter;
import com.bxkj.enterprise.mvp.presenter.NatureOfCompanyPresenter;
import com.bxkj.enterprise.ui.activity.editinfo.EEditInfoActivity;
import com.bxkj.enterprise.ui.activity.selectaddress.AddressData;
import com.bxkj.enterprise.ui.activity.selectaddress.SelectAddressActivity;
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationNavigation;
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType;
import com.therouter.router.Route;
import java.util.List;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.fillintheinfo
 * @Description: 填写资料
 * @TODO: TODO
 * @date 2018/6/21
 */
@Route(path = EnterpriseCommonRouter.FillInTheInfoActivity.URL)
public class FillInTheInfoActivity extends BaseDaggerActivity
  implements FillInTheInfoContract.View, IndustryContract.View, NatureOfCompanyContract.View,
  EnterpriseInfoContract.View {

  private static final int TO_SELECT_ADDRESS_CODE = 1;

  private static final int TO_EDIT_COMPANY_PROFILE_CODE = 2;

  @Inject
  FillInTheInfoPresenter mFillInTheInfoPresenter;

  @Inject
  EnterpriseInfoPresenter mEnterpriseInfoPresenter;

  @Inject
  IndustryPresenter mIndustryPresenter;

  @Inject
  NatureOfCompanyPresenter mNatureOfCompanyPresenter;

  private TextView tvDetailsAddress;

  private TextView tvCompanyIndustry;

  private TextView tvCompanyNature;

  private TextView tvCompanySize;

  private ClearEditText etName;

  private ClearEditText etContractPhone;

  private ClearEditText etCompanyName;

  private TextView tvCompanyProfile;

  private TextView tvComplete;

  private OptionsPickerPopup mCompanyNaturePicker;

  private OptionsPickerView mCompanySizePicker;

  private OptionsPickerView mIndustryPicker;

  private EnterpriseInfoData mEnterpriseInfoData;

  private int mUserId;

  public static void start(Context context) {
    Intent starter = new Intent(context, FillInTheInfoActivity.class);
    context.startActivity(starter);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_fill_in_the_info;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mFillInTheInfoPresenter);
    presenters.add(mEnterpriseInfoPresenter);
    presenters.add(mIndustryPresenter);
    presenters.add(mNatureOfCompanyPresenter);
    return presenters;
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());
    int pageType = getIntent().getIntExtra(EnterpriseCommonRouter.TO_ACTIVITY_TYPE,
      EnterpriseCommonRouter.FillInTheInfoActivity.CREATE);
    mUserId = getIntent().getIntExtra(EnterpriseCommonRouter.FillInTheInfoActivity.USER_ID,
      ECommonApiConstants.NO_DATA);
    if (pageType == EnterpriseCommonRouter.FillInTheInfoActivity.CREATE) {
      mEnterpriseInfoData = new EnterpriseInfoData();
      mEnterpriseInfoData.setUid(mUserId);
      if (!CheckUtils.isNullOrEmpty(UserUtils.getUserRegisterPhone())) {
        etContractPhone.setText(UserUtils.getUserRegisterPhone());
      }
    } else {
      mEnterpriseInfoPresenter.getEnterpriseInfo(getMUserID());
    }
    CheckUtils.checkEditTextIsFill(tvComplete, etName, etContractPhone, etCompanyName,
      tvDetailsAddress, tvCompanyProfile);
    mNatureOfCompanyPresenter.getNatureOfCompany();
    mIndustryPresenter.getIndustryList();
    initCompanySizePicker();
  }

  private void initCompanySizePicker() {
    List<PickerOptionsData> companySizeList = PickerUtils.parsePickerDataList(
      getResources().getStringArray(R.array.account_company_size));
    mCompanySizePicker = PickerUtils.applyMyConfig(
      new OptionsPickerBuilder(this, (options1, options2, options3, v) -> {
        mEnterpriseInfoData.setSizeid(options1 + 1);
        tvCompanySize.setText(companySizeList.get(options1).getName());
      })).build();
    mCompanySizePicker.setPicker(companySizeList);
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_details_address) {
      toSelectAddressActivity();
    } else if (view.getId() == R.id.tv_company_profile) {
      startActivityForResult(
        EEditInfoActivity.newIntent(this, getString(R.string.account_company_profile)
          , getString(R.string.please_enter_company_info),
          tvCompanyProfile.getText().toString(), 2000), TO_EDIT_COMPANY_PROFILE_CODE);
    } else if (view.getId() == R.id.tv_company_industry) {
      if (mIndustryPicker != null) {
        mIndustryPicker.show();
      }
    } else if (view.getId() == R.id.tv_company_nature) {
      if (mCompanyNaturePicker != null) {
        mCompanyNaturePicker.show();
      }
    } else if (view.getId() == R.id.tv_company_size) {
      mCompanySizePicker.show();
    } else {
      submitInfo();
    }
  }

  private void toSelectAddressActivity() {
    AddressData nowAddressData =
      new AddressData(mEnterpriseInfoData.getProvinceData(), mEnterpriseInfoData.getCityData(),
        mEnterpriseInfoData.getAreaData(), mEnterpriseInfoData.getStreetData());
    startActivityForResult(
      SelectAddressActivity.newIntent(this, nowAddressData, mEnterpriseInfoData.getAddress()
        , mEnterpriseInfoData.getLngLat()), TO_SELECT_ADDRESS_CODE);
  }

  private void submitInfo() {
    mEnterpriseInfoData.setLxr(etName.getText().toString());
    mEnterpriseInfoData.setPhone(etContractPhone.getText().toString());
    mEnterpriseInfoData.setName(etCompanyName.getText().toString());
    mEnterpriseInfoData.setName2(etCompanyName.getText().toString());
    mEnterpriseInfoData.setInfo(tvCompanyProfile.getText().toString());
    mFillInTheInfoPresenter.updateEnterpriseInfo(mEnterpriseInfoData);
  }

  @Override
  public void getNatureOfCompanySuccess(List<PickerOptionsData> pickerOptionsData) {
    mCompanyNaturePicker = new OptionsPickerPopup(this)
      .setFirstOptions(pickerOptionsData)
      .setOnConfirmClickListener((firstPosition, secondPosition, thirdPosition) -> {
        tvCompanyNature.setText(pickerOptionsData.get(firstPosition).getName());
        mEnterpriseInfoData.setProid(pickerOptionsData.get(firstPosition).getId());
      });
  }

  @Override
  public void getEnterpriseInfoSuccess(EnterpriseInfoData enterpriseInfoData) {
    mEnterpriseInfoData = enterpriseInfoData;
    etName.setText(enterpriseInfoData.getLxr());
    etContractPhone.setText(enterpriseInfoData.getPhone());
    etCompanyName.setText(enterpriseInfoData.getName());
    tvDetailsAddress.setText(String.format(getString(R.string.enterprise_address_format),
      enterpriseInfoData.getProvinceName(), enterpriseInfoData.getCityName()
      , enterpriseInfoData.getCountyName(), enterpriseInfoData.getTownName()));
    tvCompanyProfile.setText(enterpriseInfoData.getInfo());
    tvCompanyIndustry.setText(enterpriseInfoData.getTradeName());
    tvCompanyNature.setText(enterpriseInfoData.getProName());
    tvCompanySize.setText(enterpriseInfoData.getSizeName());
  }

  @Override
  public void getIndustryListSuccess(List<PickerOptionsData> optionsItemDataList) {
    mIndustryPicker = new OneOptionPicker(this)
      .setOptionsDataList(optionsItemDataList)
      .setOnConfirmListener(position -> {
        tvCompanyIndustry.setText(optionsItemDataList.get(position).getName());
        mEnterpriseInfoData.setTradeid(optionsItemDataList.get(position).getId());
      }).create();
  }

  @Override
  public void updateEnterpriseInfoSuccess(String companyName) {
    if (!getIntent().getBooleanExtra(EnterpriseCommonRouter.NEED_BACK, false)) {
      AuthenticationNavigation.navigate(companyName, AuthenticationType.ENTERPRISE).start();
    }
    finish();
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (resultCode == RESULT_OK && data != null) {
      if (requestCode == TO_SELECT_ADDRESS_CODE) {
        AddressData addressData = data.getParcelableExtra(SelectAddressActivity.ADDRESS_DATA);
        String detailsAddress = data.getStringExtra(SelectAddressActivity.DETAILS_ADDRESS);
        LngLat lngLat = data.getParcelableExtra(SelectAddressActivity.LNGLAT);
        mEnterpriseInfoData.setAddressData(addressData);
        mEnterpriseInfoData.setAddress(detailsAddress);
        mEnterpriseInfoData.setCoordinate(lngLat.getString());
        tvDetailsAddress.setText(String.format("%s%s", addressData.getAddress(), detailsAddress));
      } else {
        String resultText = data.getStringExtra(EEditInfoActivity.RESULT_TEXT);
        tvCompanyProfile.setText(resultText);
        mEnterpriseInfoData.setInfo(resultText);
      }
    }
  }

  private void bindView(View bindSource) {
    tvDetailsAddress = bindSource.findViewById(R.id.tv_details_address);
    tvCompanyIndustry = bindSource.findViewById(R.id.tv_company_industry);
    tvCompanyNature = bindSource.findViewById(R.id.tv_company_nature);
    tvCompanySize = bindSource.findViewById(R.id.tv_company_size);
    etName = bindSource.findViewById(R.id.et_name);
    etContractPhone = bindSource.findViewById(R.id.et_contract_phone);
    etCompanyName = bindSource.findViewById(R.id.et_company_name);
    tvCompanyProfile = bindSource.findViewById(R.id.tv_company_profile);
    tvComplete = bindSource.findViewById(R.id.btn_complete);
    bindSource.findViewById(R.id.tv_details_address).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_company_profile).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_company_industry).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_company_nature).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_company_size).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.btn_complete).setOnClickListener(v -> onViewClicked(v));
  }
}
