package com.bxkj.enterprise.ui.activity.selectsayhellojob

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.ecommon.constants.ECommonApiConstants
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.data.source.MyJobRepo
import com.bxkj.enterprise.data.source.ResumeRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: ejrzp
 * @Description:
 * @author:45457
 * @date: 2020/7/13
 * @version: V1.0
 */
class SelectSayHelloJobViewModel
  @Inject
  constructor(
    private val mJobRepo: MyJobRepo,
    private val _resumeRepo: ResumeRepo,
  ) : BaseViewModel() {
    val jobListViewModel = RefreshListViewModel()

    val toConversationEvent = MutableLiveData<VMEvent<PositionItemBean>>()

    val showNoInviteBalanceTipsCommand = MutableLiveData<VMEvent<String>>()

    val showNoMemberPermissionTipsCommand = MutableLiveData<VMEvent<String>>()

    private var _resumeId = 0

    init {
      setupJobListLoadEvent()
    }

    fun start(resumeId: Int) {
      _resumeId = resumeId
      jobListViewModel.refresh()
    }

    fun sendSayHello(jobInfo: PositionItemBean) {
      viewModelScope.launch {
        showLoading()
        _resumeRepo
          .sendSayHelloMsg(getSelfUserID(), jobInfo.id, _resumeId.toString(), "你好")
          .handleResult({
            toConversationEvent.value = VMEvent(jobInfo)
          }, {
            when (it.errCode) {
              30004, 30007 -> {
                showNoMemberPermissionTipsCommand.value = VMEvent(it.errMsg)
              }

              30005, 30008 -> {
                showNoInviteBalanceTipsCommand.value = VMEvent(it.errMsg)
              }

              else -> {
                showToast(it.errMsg)
              }
            }
          }, {
            hideLoading()
          })
      }
    }

    private fun setupJobListLoadEvent() {
      jobListViewModel.setOnLoadDataListener { currentPage ->
        viewModelScope.launch {
          mJobRepo
            .getReleasedJobList(
              getSelfUserID(),
              3,
              currentPage,
              ECommonApiConstants.DEFAULT_PAGE_SIZE,
            ).handleResult({
              if (it.isNullOrEmpty()) {
                jobListViewModel.noMoreData()
              } else {
                jobListViewModel.autoAddAll(it)
                if (it.size == 1) {
                  sendSayHello(it[0])
                }
              }
            }, {
              if (it.isNoDataError) {
                jobListViewModel.noMoreData()
              } else {
                jobListViewModel.loadError()
              }
            })
        }
      }
    }
  }
