<?xml version="1.0" encoding="utf-8"?>
<resources>

  <!--文字-->
  <style name="Text.DialogTitle" parent="Text.18sp.333333.Bold" />

  <style name="Text" parent="wrap_wrap">
    <item name="android:lineSpacingExtra">@dimen/dp_4</item>
  </style>

  <style name="Text.10sp">
    <item name="android:textSize">@dimen/common_sp_10</item>
  </style>

  <style name="Text.10sp.FFFFFF">
    <item name="android:textColor">@color/common_white</item>
  </style>

  <style name="Text.10sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.10sp.FE6600">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.10sp.E05261">
    <item name="android:textColor">#E05261</item>
  </style>

  <style name="Text.10sp.888888">
    <item name="android:textColor">@color/common_888888</item>
  </style>

  <style name="Text.11sp">
    <item name="android:textSize">11dp</item>
  </style>

  <style name="Text.11sp.666666">
    <item name="android:textColor">@color/common_666666</item>
  </style>

  <style name="Text.11sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.11sp.999999">
    <item name="android:textColor">@color/cl_999999</item>
  </style>

  <style name="Text.12sp">
    <item name="android:textSize">@dimen/common_sp_12</item>
  </style>

  <style name="Text.12sp.999999">
    <item name="android:textColor">@color/cl_999999</item>
  </style>

  <style name="Text.12sp.666666">
    <item name="android:textColor">@color/common_666666</item>
  </style>

  <style name="Text.12sp.B5B5B5">
    <item name="android:textColor">@color/common_b5b5b5</item>
  </style>

  <style name="Text.12sp.ec535b">
    <item name="android:textColor">@color/common_ec535b</item>
  </style>

  <style name="Text.12sp.CCCCCC">
    <item name="android:textColor">@color/common_cccccc</item>
  </style>

  <style name="Text.12sp.C29D78">
    <item name="android:textColor">@color/cl_C29D78</item>
  </style>

  <style name="Text.12sp.FFFFFF">
    <item name="android:textColor">@color/common_white</item>
  </style>

  <style name="Text.12sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.12sp.A3A3A3">
    <item name="android:textColor">@color/common_a3a3a3</item>
  </style>

  <style name="Text.12sp.333333.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.12sp.888888">
    <item name="android:textColor">@color/common_888888</item>
  </style>

  <style name="Text.12sp.FF7647">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.12sp.4d8fcc">
    <item name="android:textColor">@color/common_4d8fcc</item>
  </style>

  <style name="Text.12sp.10c198">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.12sp.767676">
    <item name="android:textColor">@color/common_767676</item>
  </style>

  <style name="Text.12sp.13bf97">
    <item name="android:textColor">@color/common_767676</item>
  </style>

  <style name="Text.13sp">
    <item name="android:textSize">@dimen/common_sp_13</item>
  </style>

  <style name="Text.13sp.4d8fcc">
    <item name="android:textColor">@color/common_4d8fcc</item>
  </style>

  <style name="Text.13sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.13sp.999999">
    <item name="android:textColor">@color/cl_999999</item>
  </style>

  <style name="Text.13sp.888888">
    <item name="android:textColor">@color/common_888888</item>
  </style>


  <style name="Text.14sp">
    <item name="android:textSize">@dimen/sp_14</item>
  </style>

  <style name="Text.14sp.FFFFFF">
    <item name="android:textColor">@color/common_white</item>
  </style>

  <style name="Text.14sp.FFFFFF.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.14sp.888888.SingleLine">
    <item name="android:lines">1</item>
    <item name="android:ellipsize">end</item>
  </style>

  <style name="Text.14sp.FE6600">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.14sp.FE6600.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.14sp.4d8fcc">
    <item name="android:textColor">@color/common_4d8fcc</item>
  </style>

  <style name="Text.14sp.0B73DE">
    <item name="android:textColor">@color/common_0b73de</item>
  </style>

  <style name="Text.14sp.B5B5B5">
    <item name="android:textColor">@color/common_b5b5b5</item>
  </style>

  <style name="Text.14sp.767676">
    <item name="android:textColor">@color/common_767676</item>
  </style>

  <style name="Text.14sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.14sp.333333.Bold">
    <item name="android:textStyle">bold</item>
  </style>


  <style name="Text.14sp.black">
    <item name="android:textColor">@color/common_black</item>
  </style>

  <style name="Text.14sp.888888">
    <item name="android:textColor">@color/common_888888</item>
  </style>

  <style name="Text.14sp.999999">
    <item name="android:textColor">@color/cl_999999</item>
  </style>

  <style name="Text.14sp.999999.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.14sp.666666">
    <item name="android:textColor">@color/common_666666</item>
  </style>

  <style name="Text.15sp">
    <item name="android:textSize">@dimen/common_sp_15</item>
  </style>

  <style name="Text.15sp.888888">
    <item name="android:textColor">@color/common_888888</item>
  </style>

  <style name="Text.15sp.888888.SingleLine">
    <item name="android:lines">1</item>
    <item name="android:ellipsize">end</item>
  </style>

  <style name="Text.15sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.15sp.333333.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.15sp.000000">
    <item name="android:textColor">@color/common_black</item>
  </style>

  <style name="Text.15sp.b5b5b5">
    <item name="android:textColor">@color/common_b5b5b5</item>
  </style>

  <style name="Text.16sp" parent="Text">
    <item name="android:textSize">@dimen/common_sp_16</item>
  </style>

  <style name="Text.16sp.FFFFFF">
    <item name="android:textColor">@color/common_white</item>
  </style>

  <style name="Text.16sp.000000">
    <item name="android:textColor">@color/common_black</item>
  </style>

  <style name="Text.16sp.ff7647">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.16sp.10C198">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.16sp.888888">
    <item name="android:textColor">@color/common_888888</item>
  </style>

  <style name="Text.16sp.999999">
    <item name="android:textColor">@color/cl_999999</item>
  </style>

  <style name="Text.16sp.CECECE">
    <item name="android:textColor">@color/common_cecece</item>
  </style>

  <style name="Text.16sp.49C280">
    <item name="android:textColor">@color/common_49C280</item>
  </style>

  <style name="Text.16sp.49C280.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.16sp.333333" parent="Text.16sp">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.16sp.333333.Bold" parent="Text.16sp.333333">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.16sp.767676">
    <item name="android:textColor">@color/common_767676</item>
  </style>

  <style name="Text.16sp.666666">
    <item name="android:textColor">@color/common_666666</item>
  </style>

  <style name="Text.17sp">
    <item name="android:textSize">@dimen/common_sp_17</item>
  </style>

  <style name="Text.18sp.333333.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.17sp.000000">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.17sp.000000.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.17sp.FE6600">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.18sp" parent="Text">
    <item name="android:textSize">@dimen/common_sp_18</item>
  </style>

  <style name="Text.18sp.FFFFFF" parent="Text.18sp">
    <item name="android:textColor">@color/common_white</item>
  </style>

  <style name="Text.18sp.FFFFFF.Bold" parent="Text.18sp.FFFFFF">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.18sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.18sp.FE6600">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.18sp.FE6600.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.20sp">
    <item name="android:textSize">@dimen/common_sp_20</item>
  </style>

  <style name="Text.20sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.20sp.FFFFFF">
    <item name="android:textColor">@color/common_white</item>
  </style>

  <style name="Text.20sp.FE6600">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.20sp.FE6600.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.20sp.333333.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.22sp">
    <item name="android:textSize">@dimen/common_sp_22</item>
  </style>

  <style name="Text.22sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.22sp.FF7405">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.22sp.333333.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.22sp.FFFFFF">
    <item name="android:textColor">@color/common_white</item>
  </style>

  <style name="Text.24sp">
    <item name="android:textSize">@dimen/common_sp_24</item>
  </style>

  <style name="Text.24sp.FFFFFF">
    <item name="android:textColor">@color/common_white</item>
  </style>

  <style name="Text.24sp.FFFFFF.Bold">
    <item name="android:textStyle">bold</item>
  </style>

  <style name="Text.24sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.28sp">
    <item name="android:textSize">@dimen/sp_28</item>
  </style>

  <style name="Text.28sp.333333">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="match_match">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">match_parent</item>
  </style>

  <style name="match_wrap">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">wrap_content</item>
  </style>

  <style name="wrap_match">
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">match_parent</item>
  </style>

  <style name="wrap_wrap">
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">wrap_content</item>
  </style>

  <!--*********View*********-->

  <style name="View" />

  <style name="View.RedTextStartTag">
    <item name="android:layout_width">@dimen/dp_3</item>
    <item name="android:layout_height">@dimen/dp_8</item>
    <item name="android:background">@color/cl_ff7405</item>
  </style>

  <!--*********分割线**********-->
  <style name="Line" />

  <style name="Line.Horizontal">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">1.5px</item>
    <item name="android:background">@color/common_e8e8e8</item>
  </style>

  <style name="Line.Horizontal.Light">
    <item name="android:layout_width">match_parent</item>
    <item name="android:background">@color/common_f4f4f4</item>
    <item name="android:layout_height">1.5px</item>
  </style>

  <style name="Line.Horizontal.Margin12OfStartAndEnd" parent="Line.Horizontal.Light">
    <item name="android:layout_marginStart">@dimen/dp_12</item>
    <item name="android:layout_marginEnd">@dimen/dp_12</item>
  </style>

  <style name="Line.Vertical">
    <item name="android:layout_width">1.5px</item>
    <item name="android:layout_height">match_parent</item>
    <item name="android:background">@color/common_e8e8e8</item>
  </style>

  <style name="Line.Vertical.Light">
    <item name="android:background">@drawable/bg_f4f4f4</item>
  </style>

  <style name="Line.JobDetailsDivider" parent="Line.Horizontal">
    <item name="android:layout_height">@dimen/dp_8</item>
    <item name="android:background">@color/common_f4f4f4</item>
  </style>

  <!--布局-->
  <style name="Layout" />

  <style name="Layout.TitleBar">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">@dimen/common_dp_44</item>
  </style>

  <style name="Layout.UserInfoItem" parent="match_wrap">
    <item name="android:gravity">center_vertical</item>
    <item name="android:layout_height">@dimen/dp_52</item>
    <item name="android:paddingEnd">@dimen/dp_10</item>
  </style>

  <style name="Layout.InfoItem">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">@dimen/dp_52</item>
    <item name="android:orientation">horizontal</item>
    <item name="android:gravity">center_vertical</item>
    <item name="android:paddingStart">@dimen/dp_12</item>
    <item name="android:paddingEnd">@dimen/dp_12</item>
  </style>

  <style name="Layout.JobSearch">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">@dimen/common_dp_44</item>
    <item name="android:layout_marginStart">@dimen/dp_12</item>
    <item name="android:layout_marginEnd">@dimen/dp_12</item>
    <item name="android:layout_marginTop">@dimen/dp_10</item>
    <item name="android:gravity">center_vertical</item>
  </style>

  <style name="Layout.ItemInfo">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">@dimen/dp_52</item>
    <item name="android:paddingStart">@dimen/dp_12</item>
    <item name="android:paddingEnd">@dimen/dp_12</item>
    <item name="android:orientation">horizontal</item>
    <item name="android:gravity">center_vertical</item>
  </style>

  <!--************RadioButton***************-->

  <style name="RadioButton" parent="wrap_wrap" />

  <style name="RadioButton.GenderSelector" parent="wrap_match">
    <item name="android:layout_gravity">center_vertical</item>
    <item name="android:button">@null</item>
    <item name="android:drawablePadding">@dimen/dp_8</item>
    <item name="android:textColor">@color/cl_b5b5b5_to_333333_selector</item>
  </style>

  <style name="RadioButton.FeedBackType">
    <item name="android:width">@dimen/dp_50</item>
    <item name="android:layout_height">28dp</item>
    <item name="android:gravity">center</item>
    <item name="android:textColor">@color/common_item_text_checked</item>
    <item name="android:layout_marginStart">@dimen/dp_10</item>
    <item name="android:button">@null</item>
    <item name="android:textSize">@dimen/common_sp_12</item>
    <item name="android:background">@drawable/personal_bg_feedback_type</item>
  </style>

  <!--**********CheckBox***********-->

  <style name="CheckBox.JobDetailsBottom" parent="wrap_wrap">
    <item name="android:textSize">@dimen/common_sp_12</item>
    <item name="android:textColor">@color/common_item_text_checked</item>
    <item name="android:layout_marginStart">@dimen/dp_22</item>
    <item name="android:drawablePadding">@dimen/dp_4</item>
    <item name="android:button">@null</item>
    <item name="android:gravity">center</item>
  </style>

  <!--***********EditText************-->

  <style name="EditText" parent="Widget.AppCompat.EditText" />

  <style name="EditText.Basic">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_weight">1</item>
    <item name="android:layout_height">@dimen/dp_52</item>
    <item name="android:singleLine">true</item>
    <item name="android:textSize">16sp</item>
    <item name="android:textCursorDrawable">@drawable/common_cursor</item>
    <item name="android:drawablePadding">@dimen/common_dp_5</item>
    <item name="android:gravity">center_vertical</item>
    <item name="android:textColorHint">?android:textColorHint</item>
    <item name="android:background">@null</item>
  </style>

  <style name="EditText.Basic.RightAlignment">
    <item name="android:gravity">end|center_vertical</item>
  </style>

  <style name="EditText.JobSearch">
    <item name="android:layout_marginStart">@dimen/dp_10</item>
    <item name="android:layout_width">0dp</item>
    <item name="android:background">@null</item>
    <item name="android:layout_weight">1</item>
    <item name="android:layout_height">match_parent</item>
    <item name="android:textColorHint">@color/common_b5b5b5</item>
    <item name="android:textColor">@color/cl_333333</item>
    <item name="android:layout_marginEnd">@dimen/dp_20</item>
    <item name="android:hint">@string/account_search_job_type</item>
    <item name="android:textSize">@dimen/dp_14</item>
    <item name="android:singleLine">true</item>
  </style>

  <style name="EditText.PublishItem">
    <item name="android:textCursorDrawable">@drawable/common_ic_custom_cursor</item>
    <item name="android:layout_width">@dimen/dp_0</item>
    <item name="android:layout_weight">1</item>
    <item name="android:drawablePadding">@dimen/dp_8</item>
    <item name="android:gravity">end|center_vertical</item>
    <item name="android:background">@null</item>
    <item name="android:layout_height">match_parent</item>
    <item name="android:textSize">@dimen/common_sp_15</item>
    <item name="android:singleLine">true</item>
    <item name="android:textColorHint">@color/common_b5b5b5</item>
    <item name="android:textColor">@color/common_888888</item>
  </style>

  <!--***********Text*************-->

  <style name="Text.FormHeader">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">@dimen/dp_36</item>
    <item name="android:background">@color/common_f4f4f4</item>
    <item name="android:drawablePadding">@dimen/common_dp_5</item>
    <item name="android:drawableStart">@drawable/shape_vertical_line_form_header_ff7647</item>
    <item name="android:gravity">center_vertical</item>
    <item name="android:paddingStart">@dimen/dp_12</item>
  </style>

  <style name="Text.InfoItem" parent="Text.15sp.888888">
    <item name="android:layout_weight">1</item>
    <item name="android:gravity">end|center_vertical</item>
    <item name="android:layout_height">match_parent</item>
    <item name="android:lines">1</item>
    <item name="android:ellipsize">end</item>
  </style>

  <style name="Text.InfoItem.Select">
    <item name="android:hint">@string/common_please_select</item>
    <item name="android:textColor">@color/cl_333333</item>
    <item name="android:textColorHint">@color/common_b5b5b5</item>
    <item name="android:drawableEnd">@drawable/common_ic_next</item>
    <item name="android:drawablePadding">@dimen/common_dp_5</item>
  </style>

  <style name="Text.JobSelectionPlaceHolder" parent="wrap_wrap">
    <item name="android:textSize">@dimen/dp_18</item>
    <item name="android:textColor">@color/common_b5b5b5</item>
    <item name="android:drawableStart">@drawable/account_ic_job_selection_placeholder</item>
    <item name="android:drawablePadding">@dimen/dp_10</item>
  </style>

  <style name="Text.JobResultFilter">
    <item name="android:textSize">@dimen/dp_14</item>
    <item name="android:textColor">@color/common_item_text_selector</item>
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">match_parent</item>
    <item name="android:drawableEnd">@drawable/personal_ic_down_arrow</item>
    <item name="android:drawablePadding">@dimen/common_dp_5</item>
    <item name="android:gravity">center</item>
  </style>

  <style name="Text.JobDetailsItemTitle" parent="Text.18sp.333333">
    <item name="android:drawableStart">@drawable/personal_ic_red_rectangle</item>
    <item name="android:gravity">center_vertical</item>
    <item name="android:drawablePadding">@dimen/dp_8</item>
    <item name="android:layout_marginTop">@dimen/dp_16</item>
  </style>

  <style name="Text.SearchJobOptions" parent="Text.15sp">
    <item name="android:textColor">@color/cl_333333</item>
    <item name="android:textColorHint">@color/common_b5b5b5</item>
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">@dimen/dp_52</item>
    <item name="android:gravity">center_vertical</item>
    <item name="android:drawablePadding">@dimen/dp_10</item>
    <item name="android:layout_marginStart">@dimen/dp_30</item>
    <item name="android:layout_marginEnd">@dimen/dp_30</item>
    <item name="android:drawableEnd">@drawable/common_ic_next</item>
    <item name="android:paddingStart">@dimen/dp_12</item>
    <item name="android:paddingEnd">@dimen/dp_12</item>
  </style>

  <style name="Text.DeleteResumeItem">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">@dimen/common_dp_42</item>
    <item name="android:layout_marginStart">@dimen/dp_30</item>
    <item name="android:layout_marginBottom">@dimen/dp_16</item>
    <item name="android:layout_marginEnd">@dimen/dp_30</item>
    <item name="android:layout_marginTop">@dimen/dp_30</item>
    <item name="android:gravity">center</item>
    <item name="android:textSize">@dimen/dp_16</item>
    <item name="android:textColor">@color/cl_ff7405</item>
    <item name="android:background">@drawable/common_bg_rounded_rectangle_line_ff865d</item>
  </style>

  <style name="Text.InputTips" parent="Text.12sp.888888">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">@dimen/dp_36</item>
    <item name="android:gravity">center_vertical</item>
    <item name="android:paddingStart">@dimen/dp_14</item>
    <item name="android:background">@color/common_f4f4f4</item>
  </style>

  <style name="Text.InputTips.FE6600">
    <item name="android:textColor">@color/cl_ff7405</item>
  </style>

  <style name="Text.MineItemOption" parent="Text.15sp.333333">
    <item name="android:paddingStart">@dimen/dp_12</item>
    <item name="android:paddingEnd">@dimen/dp_12</item>
    <item name="android:layout_height">@dimen/dp_52</item>
    <item name="android:layout_width">match_parent</item>
    <item name="android:drawableEnd">@drawable/common_ic_next</item>
    <item name="android:drawablePadding">@dimen/dp_10</item>
    <item name="android:gravity">center_vertical</item>
  </style>

  <style name="Text.MessageGroupItem" parent="Text.14sp.333333">
    <item name="android:paddingTop">@dimen/dp_16</item>
    <item name="android:paddingBottom">@dimen/dp_16</item>
    <item name="android:drawablePadding">@dimen/dp_12</item>
    <item name="android:gravity">center</item>
  </style>

  <style name="Text.MineApplicationRecordItem" parent="Text.12sp.333333">
    <item name="android:layout_weight">1</item>
    <item name="android:gravity">center</item>
    <item name="android:drawablePadding">@dimen/dp_4</item>
  </style>

  <style name="Text.JobSelectionFirstClass" parent="Text.15sp">
    <item name="android:layout_marginStart">@dimen/dp_12</item>
    <item name="android:layout_height">@dimen/dp_0</item>
    <item name="android:layout_weight">1</item>
    <item name="android:gravity">center_vertical</item>
    <item name="android:maxLines">2</item>
    <item name="android:ellipsize">end</item>
    <item name="android:layout_marginEnd">@dimen/dp_12</item>
    <item name="android:textColor">@color/common_job_selection_first_class_text_selector</item>
  </style>

  <style name="Text.JobSelectionSecondClass">
    <item name="android:textSize">@dimen/common_sp_12</item>
    <item name="android:layout_width">match_parent</item>
    <item name="android:gravity">center</item>
    <item name="android:maxLines">2</item>
    <item name="android:ellipsize">end</item>
    <item name="android:background">@color/common_f8f8f8</item>
    <item name="android:layout_height">@dimen/common_dp_44</item>
    <item name="android:textColor">@color/common_item_text_selector</item>
  </style>

  <style name="Text.BigInfoTitle" parent="Text.18sp.333333.Bold">
    <item name="android:layout_width">match_parent</item>
  </style>

  <style name="Text.NormalInfoTitle" parent="Text.16sp.333333.Bold" />

  <style name="Text.Tertiary" parent="Text.12sp.888888" />

  <style name="Text.Tertiary.White">
    <item name="android:textColor">@color/common_white</item>
  </style>

  <style name="Text.Tertiary.Dark">
    <item name="android:textColor">@color/cl_333333</item>
  </style>

  <style name="Text.ContentText" parent="Text.15sp.333333" />

  <!--*********按钮*********-->
  <style name="Button" parent="match_wrap" />

  <style name="Button.Basic">
    <item name="android:textSize">@dimen/common_sp_17</item>
    <item name="android:gravity">center</item>
    <item name="android:layout_height">@dimen/common_dp_42</item>
    <item name="android:textColor">@color/common_button_text_selector</item>
    <item name="android:background">@drawable/common_bg_basic_btn_selector</item>
  </style>

  <style name="Button.Basic.Round">
    <item name="android:background">@drawable/common_bg_basic_round_btn_selector</item>
  </style>

  <style name="Button.Follow">
    <item name="android:textSize">@dimen/common_sp_12</item>
    <item name="android:layout_width">55dp</item>
    <item name="android:background">@drawable/bg_btn_follow_selector</item>
    <item name="android:gravity">center</item>
    <item name="android:paddingTop">@dimen/dp_4</item>
    <item name="android:paddingBottom">@dimen/dp_4</item>
    <item name="android:textColor">@color/cl_btn_follow_selector</item>
  </style>

  <!--++++++++++ ImageView Start ++++++++++-->
  <style name="ImageView" />

  <style name="ImageView.UserAvatar">
    <item name="android:layout_width">44dp</item>
    <item name="android:layout_height">44dp</item>
    <item name="android:background">@drawable/frame_f4f4f4_round</item>
    <item name="android:padding">@dimen/dp_1</item>
  </style>


  <!--++++++++++ ImageView End ++++++++++-->

  <!--++++++++++ FrameLayout Start ++++++++++-->

  <!--++++++++++ FrameLayout End ++++++++++-->

</resources>