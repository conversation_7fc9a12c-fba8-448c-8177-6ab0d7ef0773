package com.bxkj.account.ui.login

import android.content.DialogInterface
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import com.bxkj.account.ui.login.LoginMethod.SMS_CODE
import com.bxkj.account.ui.recoverpassword.RecoverPasswordActivity
import com.bxkj.account.ui.register.RegisterNavigation
import com.bxkj.accountmodule.R
import com.bxkj.accountmodule.databinding.AccountActivityLoginV2Binding
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.constants.RouterConstants
import com.bxkj.common.util.ActivityRouterUtils
import com.bxkj.common.util.TencentSdkManager
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.BaseDialogFragment.OnDismissListener
import com.bxkj.common.widget.dialog.PrivacyDialog
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.common.widget.system.ZPClickableSpan
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoNavigation
import com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo.SchoolInfoNavigation
import com.bxkj.jrzp.user.selectidentity.SelectIdentityNavigation
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType
import com.bxkj.jrzp.user.ui.idcardvalidation.IDCardValidationNavigation
import com.bxkj.jrzp.yd_login.YDLoginManager
import com.bxkj.jrzp.yd_login.YDLoginManager.OnQuickLoginListener
import com.bxkj.jrzp.yd_login.data.YDLoginResultData
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.elvishew.xlog.XLog
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.therouter.router.Route
import java.lang.ref.WeakReference
import java.util.regex.Pattern

/**
 * 登录
 * @author: sanjin
 * @date: 2022/3/4
 */
@Route(path = RouterNavigation.LoginActivity.URL)
class LoginV2Activity : BaseDBActivity<AccountActivityLoginV2Binding, LoginV2ViewModel>(),
  View.OnClickListener {

  companion object {

    const val TO_BIND_PHONE_CODE = 1
  }

  private var _quickLoginListener: OnQuickLoginListener? = null

  override fun getViewModelClass(): Class<LoginV2ViewModel> = LoginV2ViewModel::class.java

  override fun getLayoutId(): Int = R.layout.account_activity_login_v2

  override fun initPage(savedInstanceState: Bundle?) {
    statusBarManager.titleBar(viewBinding.llTitleBar).statusBarDarkFont(true, 0.4f).init()

    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    handleSoftKeyboard()

    setupPrivacyNoteClickListener()

    subscribeCertificationSuccessMsg()
    subscribeViewModelEvent()

    initView()

    viewModel.start(
      getIntentLoginType(),
      getIntentCreateResume(),
      intent.getBooleanExtra(LoginNavigation.EXTRA_NEED_BACK, true)
    )

    ydQuickLogin()
  }

  override fun onNewIntent(intent: Intent) {
    super.onNewIntent(intent)
    checkIsWechatLoginSuccess(intent)
    checkHasLoginPhone(intent)
  }

  private fun subscribeCertificationSuccessMsg() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe { message: RxBus.Message ->
          if (message.code == RxMsgCode.ACTION_CERTIFICATION_SUBMIT_SUCCESS) {
            viewModel.toNextPage()
          }
        })
  }

  private fun checkIsWechatLoginSuccess(intent: Intent?) {
    if (intent?.getBooleanExtra(
        RouterNavigation.LoginActivity.EXTRA_IS_WECHAT_LOGIN_SUCCESS,
        false
      ) == true
    ) {
      viewModel.checkUserBindPhone()
    }
  }

  private fun checkHasLoginPhone(intent: Intent?) {
    intent?.let {
      val loginPhoneNumber = intent.getStringExtra(RouterNavigation.LoginActivity.LOGIN_PHONE)
      viewModel.existedPhoneLogin(loginPhoneNumber)
    }
  }

  private fun initView() {
    if (AuthenticationType.higherEnterpriseAuth(getIntentLoginType())) {
      viewBinding.tvLoginWithWechat.visibility = View.GONE
    }
  }

  private fun getIntentLoginType(): Int {
    return intent.getIntExtra(
      LoginNavigation.EXTRA_LOGIN_TYPE,
      AuthenticationType.QUERY_HIGHER_AUTH
    )
  }

  private fun getIntentCreateResume(): Boolean {
    return intent.getBooleanExtra(LoginNavigation.EXTRA_LOGIN_FOR_CREATE_RESUME, false)
  }

  override fun onClick(v: View?) {
    v?.let {
      when (v.id) {
        R.id.iv_close -> {
          finish()
        }

        R.id.tv_login_with_phone -> {
          ydQuickLogin()
        }

        R.id.tv_register, R.id.tv_register_now -> {
          if (getIntentLoginType() == AuthenticationType.UN_AUTH_CODE) {
            SelectIdentityNavigation.navigate(true).start()
          } else {
            RegisterNavigation.create(getIntentLoginType()).start()
          }
        }

        R.id.tv_forget_pwd -> {
          startActivity(Intent(this, RecoverPasswordActivity::class.java))
        }
      }
    }
  }

  private fun loginWithWechat() {
    if (TencentSdkManager.getWxApi().isWXAppInstalled) {
      val req = SendAuth.Req()
      req.scope = TencentSdkManager.WX_LOGIN_SCOPE
      req.state = TencentSdkManager.WX_LOGIN_STATE_NEED_BACK
      TencentSdkManager.getWxApi().sendReq(req)
    } else {
      showToast(getString(R.string.wx_no_installed_tips))
    }
  }

  private fun ydQuickLogin() {
    showLoading()
    if (_quickLoginListener == null) {
      initQuickLoginListener()
    }
    _quickLoginListener?.let {
      YDLoginManager.onePass(it)
    } ?: this.hiddenLoading()
  }

  private fun initQuickLoginListener() {
    _quickLoginListener = object : OnQuickLoginListener() {
      val activity = WeakReference(this@LoginV2Activity)
      override fun onLoginPageLoaded() {
        activity.get()?.hiddenLoading()
      }

      override fun onCancelLogin() {
        activity.get()?.hiddenLoading()
      }

      override fun onLoginSuccess(loginResultData: YDLoginResultData) {
        activity.get()?.viewModel?.handleLoginResultData(
          loginResultData.ubName2, loginResultData.id,
          loginResultData.type2
        )
      }

      override fun onLoginFailed(errMsg: String?): Boolean {
        activity.get()?.let { activeActivity ->
          activeActivity.hiddenLoading()
          errMsg?.let {
            activeActivity.showToast(it)
          } ?: let {
            activeActivity.showToast("一键登录跳转失败，请选择其他登录方式")
          }
        }
        return super.onLoginFailed(errMsg)
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.showPrivacyAgreeCommand.observe(this) {
      val dialogContent = getString(R.string.account_login_privacy_tips_content)
      val dialogContentBuilder = SpannableStringBuilder(dialogContent)
      val pattern = Pattern.compile("《(.*?)》")
      val matcher = pattern.matcher(dialogContent)
      while (matcher.find()) {
        XLog.d(matcher.group(1))
        val clickableSpan = if (matcher.group(1) == "服务协议") {
          getClickableSpan {
            ActivityRouterUtils.toAgreementActivity()
          }
        } else {
          getClickableSpan {
            ActivityRouterUtils.toPrivacyActivity()
          }
        }
        dialogContentBuilder.setSpan(
          clickableSpan,
          matcher.start(),
          matcher.end(),
          Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
      }

      PrivacyDialog(
        title = getString(R.string.account_login_privacy_tips_title),
        content = dialogContentBuilder,
        cancelText = getString(R.string.disagree),
        confirmText = getString(R.string.agree),
        onCancelClicked = {
          it.dismiss()
        },
        onConfirmClicked = {
          it.dismiss()
          viewModel.switchPrivacyStatus()
        }).show(supportFragmentManager)

//            viewBinding.cbPrivacy.post {
//                val balloon = Balloon.Builder(this)
//                    .setText(getString(R.string.account_login_privacy_tips))
//                    .setArrowSize(8)
//                    .setPadding(8)
//                    .setArrowPosition(0.07f)
//                    .setBackgroundColorResource(R.color.colorLabelYellow)
//                    .setAutoDismissDuration(2000)
//                    .setLifecycleOwner(this)
//                if (QMUIKeyboardHelper.isKeyboardVisible(this)) {
//                    balloon.setMarginLeft(20).build().showAlignTop(viewBinding.cbPrivacy)
//                } else {
//                    balloon.build().showAsDropDown(viewBinding.cbPrivacy)
//                }
//            }
//
//            ObjectAnimator.ofFloat(
//                viewBinding.llPrivacyTips,
//                "translationX",
//                0f,
//                25f,
//                -25f,
//                25f,
//                -25f,
//                15f,
//                -15f,
//                6f,
//                -6f,
//                0f
//            ).setDuration(700).start()
    }

    viewModel.showResumeCountLimitCommand.observe(this, Observer {
      TipsDialog().setTitle(getString(R.string.my_resume_create_up_to_six))
        .setOnConfirmClickListener { dialogFragment: DialogFragment? ->
          toMainPage()
        }
        .setOnOverrideDismissListener(object : OnDismissListener {
          override fun onDismiss(dialogInterface: DialogInterface?) {
            toMainPage()
          }
        })
        .show(supportFragmentManager, TipsDialog.TAG)
    })

    viewModel.toCreateResumeCommand.observe(this, Observer {
      toCreateResumePage()
    })

    viewModel.toMainPageCommand.observe(this, Observer {
      toMainPage()
    })

    viewModel.toCompleteUserInfoCommand.observe(this, Observer {
      toCompleteUserInfoPage()
    })

    viewModel.backToPreviousPageCommand.observe(this, Observer {
      backToPreviousPage()
    })

    viewModel.toAuthPageCommand.observe(this, Observer {
      when {
        AuthenticationType.isSchool(it) -> {
          SchoolInfoNavigation.navigate(SchoolInfoNavigation.NEXT_STEP_AUTH, true).start()
        }

        it == AuthenticationType.SELF_EMPLOYED -> {
          IDCardValidationNavigation.create(IDCardValidationNavigation.NEXT_STEP_TO_MAIN).start()
        }

        else -> {
          BusinessBasicInfoNavigation.navigate(true, AuthenticationType.ENTERPRISE, false).start()
        }
      }
    })

    viewModel.loginWithWechatCommand.observe(this, Observer {
      loginWithWechat()
    })

    viewModel.toBindPhoneCommand.observe(this, Observer {
      Router.getInstance()
        .to(RouterNavigation.BindMobileActivity.URL)
        .startForResult(this, TO_BIND_PHONE_CODE)
    })

    viewModel.showLongTimeNoLoginTipsCommand.observe(this, EventObserver {
      TipsDialog()
        .setContent(it)
        .setOnConfirmClickListener { dialogFragment: DialogFragment? ->
          viewModel.setLoginMethod(SMS_CODE)
        }
        .show(supportFragmentManager, TipsDialog.TAG)
    })
  }

  private fun getClickableSpan(onClick: () -> Unit): ClickableSpan {
    return object : ClickableSpan() {
      override fun onClick(widget: View) {
        onClick.invoke()
      }

      override fun updateDrawState(ds: TextPaint) {
        ds.color = getResColor(R.color.common_colorPrimary)
        ds.isUnderlineText = false
      }
    }
  }

  private fun handleSoftKeyboard() {
    val rootView = (findViewById<View>(android.R.id.content) as ViewGroup).getChildAt(0)
    rootView.viewTreeObserver.addOnGlobalLayoutListener {
      val rect = Rect()
      rootView.getWindowVisibleDisplayFrame(rect)
      val visibleHeight = rootView.rootView.height - rect.bottom
      if (visibleHeight > 150) {
        val location = IntArray(2)
        viewBinding.llPrivacyTips.getLocationInWindow(location)
        val scrollHeight: Int =
          location[1] + viewBinding.llPrivacyTips.height - rect.bottom
        if (scrollHeight > 0) {
          rootView.scrollTo(0, scrollHeight)
        }
      } else {
        rootView.scrollTo(0, 0)
      }
    }
  }

  private fun setupPrivacyNoteClickListener() {
    val spannableStringBuilder =
      SpannableStringBuilder(getString(R.string.account_login_privacy_note))
    val agreementClickSpan: ClickableSpan =
      ZPClickableSpan({ ActivityRouterUtils.toAgreementActivity() }, getResColor(R.color.cl_ff7405))

    val privacyAgreementClickSpan: ClickableSpan =
      ZPClickableSpan({ ActivityRouterUtils.toPrivacyActivity() }, getResColor(R.color.cl_ff7405))

    spannableStringBuilder.setSpan(
      agreementClickSpan,
      8,
      14,
      Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    spannableStringBuilder.setSpan(
      privacyAgreementClickSpan, 15, 21,
      Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    viewBinding.tvPrivacy.movementMethod = LinkMovementMethod.getInstance()
    viewBinding.tvPrivacy.text = spannableStringBuilder
  }

  //============================== 页面跳转 ==============================//

  private fun toCreateResumePage() {
    Router.getInstance()
      .to(RouterNavigation.CreateResumeStepTwoActivity.URL)
      .withInt(
        RouterConstants.CREATE_RESUME_FROM,
        RouterConstants.CREATE_RESUME_FROM_REGISTER_OR_LOGIN
      )
      .start()
    finish()
  }

  private fun toMainPage() {
    Router.getInstance()
      .to(RouterNavigation.PersonalMainActivity.URL)
      .withFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
      .start()
  }

  private fun toCompleteUserInfoPage() {
    MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME_FOR_REGISTER)
      .start()
    finish()
  }

  private fun backToPreviousPage() {
    setResult(RESULT_OK)
    finish()
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

  override fun onDestroy() {
    viewBinding.tvPrivacy.movementMethod = null
    _quickLoginListener = null
    super.onDestroy()
  }
}