package com.bxkj.account.ui.register

import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.account.api.AccountApi
import com.bxkj.accountmodule.R
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.RegularUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.startCountDown
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 * Date:2024/7/17
 **/
class RegisterViewModel @Inject constructor(
  private val accountApi: AccountApi
) : BaseViewModel() {

  val phoneNumber = MutableLiveData<String>()
  val password = MutableLiveData<String>()

  val smsCode = MutableLiveData<String>()
  val smdCodeCountdown = MutableLiveData<Int>().apply { value = 0 }

  val mobileNumberHasRegisteredEvent = MutableLiveData<VMEvent<String>>()
  val registerSuccessEvent = MutableLiveData<VMEvent<Int>>()

  val agreePrivacy = MutableLiveData<Boolean>().apply { value = false }

  val registerBtnEnabled = MediatorLiveData<Boolean>().apply {

    fun updateValue() {
      value = listOf(phoneNumber, smsCode, password).none { it.value.isNullOrBlank() }
    }

    listOf(phoneNumber, smsCode, password).forEach {
      addSource(it) {
        updateValue()
      }
    }
  }

  private var registerType = 0 //0个人、1企业

  fun start(authType: Int) {
    if (AuthenticationType.higherEnterpriseAuth(authType)) {
      registerType = 1
    } else {
      registerType = 0
    }
  }

  fun checkMobileNumberIsRegistered() {
    checkAgreePrivacy {
      phoneNumber.value?.let { phoneNumber ->
        if (!RegularUtils.isMobile(phoneNumber)) {
          showToast("手机号格式有误")
          return@checkAgreePrivacy
        }
        viewModelScope.launch {
          showLoading()
          accountApi.checkMobileNumberIsRegistered(phoneNumber)
            .handleResult({
              mobileNumberHasRegisteredEvent.value = VMEvent(phoneNumber)
            }, {
              if (it.errCode == 10002) {
                requestSmsCode()
              } else {
                showToast(it.errMsg)
              }
            }, {
              hideLoading()
            })
        }
      } ?: showToast("请填写手机号码")
    }
  }

  fun switchPrivacyStatus() {
    agreePrivacy.value?.let { agree ->
      agreePrivacy.value = agree.not()
    }
  }

  fun register() {
    checkAgreePrivacy {
      phoneNumber.value?.let { phoneNumber ->
        password.value?.let { password ->
          if (!RegularUtils.isPassword(password)) {
            showToast("密码长度需为8-16，包含大小写字母和数字")
            return@checkAgreePrivacy
          }
          showLoading()
          viewModelScope.launch {
            accountApi.registerWithMobile(phoneNumber, password, UserUtils.getUserRole())
              .handleResult({
                it?.let {
                  registerSuccessEvent.value = VMEvent(it)
                } ?: let { showToast("注册出错，请重试") }
              }, {
                showToast(it.errMsg)
              }, {
                hideLoading()
              })
          }
        }
      }
    }
  }

  private fun requestSmsCode() {
    phoneNumber.value?.let {
      if (RegularUtils.isMobile(it)) {
        showLoading()
        viewModelScope.launch {
          accountApi.requestSmsCode(phoneNumber.value!!, 3)
            .handleResult({
              startCountDown(60, { countDown ->
                smdCodeCountdown.value = countDown
              }, {
                smdCodeCountdown.value = 0
              })
            }, { err ->
              showToast(err.errMsg)
            }, {
              hideLoading()
            })
        }
      } else {
        showToast("手机号格式有误")
      }
    } ?: showToast("请输入手机号")
  }

  private fun checkAgreePrivacy(agreeAction: () -> Unit) {
    agreePrivacy.value?.let {
      if (it) {
        agreeAction.invoke()
      } else {
        showToast(R.string.common_login_no_agree_privacy_tips)
      }
    }
  }
}