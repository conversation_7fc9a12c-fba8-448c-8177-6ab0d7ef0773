<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.support.feature.ui.commonsearch.CommonSearchViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <LinearLayout
      android:id="@+id/fl_search_bar"
      android:layout_width="match_parent"
      android:layout_height="52dp"
      android:gravity="center_vertical"
      android:paddingTop="@dimen/dp_6"
      android:paddingBottom="@dimen/dp_6">

      <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:onClick="@{onClickListener}"
        android:src="@drawable/common_ic_back" />

      <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:background="@drawable/bg_f4f4f4_radius_10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
          android:id="@+id/tv_search_flag"
          style="@style/Text.15sp.333333"
          android:layout_width="wrap_content"
          android:layout_height="match_parent"
          android:drawableEnd="@drawable/ic_expand"
          android:drawablePadding="@dimen/dp_2"
          android:gravity="center"
          android:paddingStart="@dimen/dp_8"
          android:paddingEnd="@dimen/dp_8"
          android:text="@{viewModel.searchFlag.name}"
          android:visibility="@{viewModel.searchFlag==null?View.GONE:View.VISIBLE}" />

        <View
          style="@style/Line.Vertical"
          android:layout_marginTop="@dimen/dp_8"
          android:layout_marginBottom="@dimen/dp_8"
          android:visibility="@{viewModel.searchFlag==null?View.GONE:View.VISIBLE}" />

        <com.bxkj.common.widget.MyEditText
          android:id="@+id/et_search_content"
          style="@style/Text.15sp.333333"
          android:layout_width="@dimen/dp_0"
          android:layout_height="@dimen/dp_40"
          android:layout_weight="1"
          android:background="@null"
          android:gravity="center_vertical"
          android:hint="@string/search_hint"
          android:imeOptions="actionSearch"
          android:paddingStart="@dimen/dp_12"
          android:paddingEnd="@dimen/dp_12"
          android:singleLine="true"
          android:text="@={viewModel.searchContent}" />

      </LinearLayout>

      <TextView
        android:id="@+id/tv_search"
        style="@style/common_Text.15sp.ff7647"
        android:onClick="@{()->viewModel.startSearch()}"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12"
        android:text="@string/search" />
    </LinearLayout>

    <View style="@style/Line.Horizontal" />

    <ScrollView style="@style/match_match">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <LinearLayout
          style="@style/match_wrap"
          android:gravity="center_vertical"
          android:orientation="horizontal"
          android:paddingStart="@dimen/dp_16"
          android:paddingEnd="@dimen/dp_16"
          android:visibility="@{viewModel.searchHistory.size()>0?View.VISIBLE:View.GONE}">

          <TextView
            style="@style/Text.16sp.888888"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_16"
            android:layout_weight="1"
            android:text="@string/search_history" />

          <TextView
            android:id="@+id/tv_clear_history"
            style="@style/Text.14sp.333333"
            android:layout_marginEnd="@dimen/dp_12"
            android:onClick="@{onClickListener}"
            android:text="@string/search_history_clear"
            android:visibility="@{viewModel.openEditHistory?View.VISIBLE:View.GONE}" />

          <TextView
            android:id="@+id/tv_confirm_edit"
            style="@style/Text.14sp.333333"
            android:onClick="@{()->viewModel.switchHistoryEditStatus(!viewModel.openEditHistory)}"
            android:text="@string/complete"
            android:visibility="@{viewModel.openEditHistory?View.VISIBLE:View.GONE}" />

          <ImageView
            android:id="@+id/iv_delete"
            style="@style/wrap_wrap"
            android:layout_width="@dimen/common_dp_32"
            android:layout_height="@dimen/common_dp_32"
            android:onClick="@{()->viewModel.switchHistoryEditStatus(!viewModel.openEditHistory)}"
            android:scaleType="center"
            android:src="@drawable/ic_delete_item"
            android:visibility="@{viewModel.openEditHistory?View.GONE:View.VISIBLE}" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_history"
          style="@style/match_wrap"
          android:overScrollMode="never"
          android:paddingStart="@dimen/dp_16"
          android:paddingEnd="@dimen/dp_16"
          bind:items="@{viewModel.searchHistory}" />
      </LinearLayout>
    </ScrollView>

  </LinearLayout>
</layout>