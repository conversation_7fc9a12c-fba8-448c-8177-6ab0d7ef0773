package com.bxkj.jrzp.support.db.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.bxkj.jrzp.support.db.entry.UploadAttachedResumeRecord;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data.db
 * @Description:
 * @TODO: TODO
 * @date 2018/12/4
 */
@Dao
public interface UploadAttachmentResumeRecordDao {
    @Query("SELECT * FROM upload_attached_resume_record_table WHERE resumeId==:resumeId AND attachedResumeWebUrl==:webUrl")
    UploadAttachedResumeRecord findRecordByResumeIdAndWebUrl(int resumeId, String webUrl);

    @Query("DELETE  FROM upload_attached_resume_record_table WHERE resumeId==:resumeId")
    void deleteRecordByResumeId(int resumeId);

    @Update
    int updateRecord(UploadAttachedResumeRecord uploadAttachedResumeRecord);

    @Insert
    void insertRecord(UploadAttachedResumeRecord uploadAttachedResumeRecord);
}
