package com.bxkj.jrzp.support.db;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import android.content.Context;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.jrzp.support.db.dao.NearbyJobDao;
import com.bxkj.jrzp.support.db.dao.SearchRecordDao;
import com.bxkj.jrzp.support.db.dao.UploadAttachmentResumeRecordDao;
import com.bxkj.jrzp.support.db.dao.UserActionDao;
import com.bxkj.jrzp.support.db.dao.UserLoginHistoryDao;
import com.bxkj.jrzp.support.db.dao.UserSelectCityHistoryDao;
import com.bxkj.jrzp.support.db.entry.NearbyJob;
import com.bxkj.jrzp.support.db.entry.SearchRecord;
import com.bxkj.jrzp.support.db.entry.UploadAttachedResumeRecord;
import com.bxkj.jrzp.support.db.entry.UserActionRecord;
import com.bxkj.jrzp.support.db.entry.UserLoginHistory;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data.db
 * @Description:
 * @TODO: TODO
 * @date 2018/12/4
 */
@Database(entities = {
  UploadAttachedResumeRecord.class, SearchRecord.class, AreaOptionsData.class,
  UserActionRecord.class, UserLoginHistory.class, NearbyJob.class
}, version = 8)
public abstract class AppDatabase extends RoomDatabase {
  private static final String DATABASE_NAME = "database_jrzp";

  private static AppDatabase INSTANCE;

  //上传附件简历记录
  public abstract UploadAttachmentResumeRecordDao uploadAttachedResumeRecordDao();

  //搜索记录
  public abstract SearchRecordDao searchRecordDao();

  //选择城市
  public abstract UserSelectCityHistoryDao userSelectCityHistoryDao();

  //用户行为
  public abstract UserActionDao userActionDao();

  //登录历史
  public abstract UserLoginHistoryDao userLoginHistoryDao();

  //附近职位坐标
  public abstract NearbyJobDao nearbyJobDao();

  private static final Migration MIGRATION_1_TO_2 = new Migration(1, 2) {
    @Override
    public void migrate(@NonNull SupportSQLiteDatabase database) {
      database.execSQL(
        "CREATE TABLE IF NOT EXISTS `table_search_record` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL"
          +
          ", `type` INTEGER NOT NULL"
          +
          ", `keyword` TEXT)");
    }
  };

  private static final Migration MIGRATION_2_TO_3 = new Migration(2, 3) {
    @Override
    public void migrate(@NonNull SupportSQLiteDatabase database) {
      database.execSQL("DROP TABLE IF EXISTS 'table_user_select_city_history'");
      database.execSQL(
        "CREATE TABLE IF NOT EXISTS `table_user_select_city_history` (`tableId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL"
          +
          ", `id` INTEGER NOT NULL"
          +
          ", `pid` INTEGER NOT NULL"
          +
          ", `pName` TEXT"
          +
          ", `type` INTEGER NOT NULL"
          +
          ", `name` TEXT)");
    }
  };

  private static final Migration MIGRATION_3_TO_4 = new Migration(3, 4) {
    @Override
    public void migrate(@NonNull SupportSQLiteDatabase database) {
      database.execSQL(
        "CREATE TABLE IF NOT EXISTS `table_user_action_record` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL"
          +
          ", `type` INTEGER NOT NULL"
          +
          ", `key` TEXT"
          +
          ", `value` FLOAT NOT NULL)");
    }
  };

  private static final Migration MIGRATION_4_TO_5 = new Migration(4, 5) {
    @Override public void migrate(@NonNull SupportSQLiteDatabase database) {
      database.execSQL(
        "CREATE TABLE IF NOT EXISTS `user_login_history` (`user_id` INTEGER PRIMARY KEY NOT NULL"
          +
          ", `avatar` TEXT"
          +
          ", `nickname` TEXT)");
    }
  };

  private static final Migration MIGRATION_5_TO_6 = new Migration(5, 6) {
    @Override public void migrate(@NonNull SupportSQLiteDatabase database) {
      database.execSQL(
        "ALTER TABLE 'user_login_history' ADD identity INTEGER NOT NULL Default 0");
    }
  };

  private static final Migration MIGRATION_6_TO_7 = new Migration(6, 7) {
    @Override public void migrate(@NonNull SupportSQLiteDatabase database) {
      database.execSQL("ALTER TABLE 'user_login_history' ADD token TEXT");
    }
  };

  private static final Migration MIGRATION_7_TO_8 = new Migration(7, 8) {
    @Override
    public void migrate(@NonNull SupportSQLiteDatabase database) {
      database.execSQL(
        "CREATE TABLE IF NOT EXISTS `table_nearby_job` (" +
          "`t_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
          "`id` INTEGER NOT NULL, " +
          "`lng` TEXT, " +
          "`lat` TEXT, " +
          "`distance` REAL NOT NULL, "+
          "`distanceText` TEXT NOT NULL)"
      );
    }
  };

  public static AppDatabase getInstance(Context context) {
    if (INSTANCE == null) {
      INSTANCE =
        Room.databaseBuilder(context.getApplicationContext(), AppDatabase.class, DATABASE_NAME)
          .allowMainThreadQueries()
          .addMigrations(
            MIGRATION_1_TO_2,
            MIGRATION_2_TO_3,
            MIGRATION_3_TO_4,
            MIGRATION_4_TO_5,
            MIGRATION_5_TO_6,
            MIGRATION_6_TO_7,
            MIGRATION_7_TO_8)
          .build();
    }
    return INSTANCE;
  }

  public static void destroyInstance() {
    INSTANCE = null;
  }
}
