<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  style="@style/match_wrap">

  <View
      android:layout_width="@dimen/dp_0"
    android:layout_height="@dimen/dp_0"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <View
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_24"
    android:background="@drawable/bg_share"
    app:layout_constraintBottom_toTopOf="@id/ll_share"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent" />

  <LinearLayout
    android:layout_width="@dimen/share_pic_size"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_share_pic"
    android:orientation="vertical"
    app:layout_constraintBottom_toTopOf="@id/ll_share"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <ImageView
      android:id="@+id/iv_share_pic"
      android:layout_width="@dimen/share_pic_size"
      android:layout_height="@dimen/share_pic_size" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_48"
      android:gravity="center_vertical"
      android:orientation="horizontal">

      <TextView
        android:id="@+id/tv_course_name"
        style="@style/Text.12sp.666666"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_14"
        android:layout_weight="1"
        android:ellipsize="end"
        android:maxLines="2" />

      <View
        style="@style/Line.Vertical"
        android:layout_height="@dimen/dp_30" />

      <TextView
        android:id="@+id/tv_course_price"
        style="@style/Text.14sp.FE6600.Bold"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_14" />

    </LinearLayout>
  </LinearLayout>

  <LinearLayout
    android:id="@+id/ll_share"
    style="@style/match_wrap"
    android:orientation="vertical"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <LinearLayout
      style="@style/match_wrap"
      android:background="@drawable/bg_ffffff"
      android:orientation="vertical"
      android:paddingTop="@dimen/dp_16">

      <com.bxkj.common.widget.marquee.MarqueeView
        android:id="@+id/marquee_payment_notice"
        style="@style/match_wrap"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_payment_notice" />

      <TextView
        android:id="@+id/tv_share_title"
        style="@style/Text.14sp.333333.Bold"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/share_commodity_tips_title" />

      <TextView
        android:id="@+id/tv_share_desc"
        style="@style/Text.14sp.666666"
        android:layout_marginStart="@dimen/dp_18"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_18"
        android:gravity="start" />

      <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_share_options"
        style="@style/match_wrap"
        android:overScrollMode="never" />

    </LinearLayout>

    <View style="@style/Line.Horizontal" />

    <TextView
      android:id="@+id/tv_cancel"
      style="@style/Text.18sp.333333.Bold"
      android:layout_width="match_parent"
      android:layout_height="@dimen/share_cancel_btn_height"
      android:background="@drawable/bg_ffffff"
      android:gravity="center"
      android:text="@string/share_cancel" />

  </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>