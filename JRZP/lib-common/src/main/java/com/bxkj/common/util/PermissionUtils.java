package com.bxkj.common.util;

import androidx.fragment.app.FragmentActivity;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.elvishew.xlog.XLog;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import java.util.List;

/**
 * @Description: 权限工具类
 * @date 2019/2/14
 */
public class PermissionUtils {

  private static final String REQUEST_DIALOG_TAG = "PERMISSION_REQUEST_DIALOG";

  public static void requestPermission(FragmentActivity activity, String tipsTitle,
    String tipsContent, OnRequestResultListener onRequestResultListener, String... permission) {

    if (activity == null || permission == null || permission.length == 0) {
      return;
    }

    if (XXPermissions.isGranted(activity, permission)) {
      if (onRequestResultListener != null) {
        onRequestResultListener.onRequestSuccess();
      }
    } else {
      new ActionDialog.Builder()
        .setTitle(tipsTitle)
        .setCancelable(false)
        .setCancelText("拒绝")
        .setConfirmText("同意")
        .setContent(tipsContent)
        .setOnConfirmClickListener((dialog) ->
          startRequestPermission(activity, onRequestResultListener, permission)
        )
        .setOnCancelClickListener(() -> {
            if (onRequestResultListener != null) {
              onRequestResultListener.onRequestFailed(null, false);
            }
          }
        )
        .build()
        .show(activity.getSupportFragmentManager(), REQUEST_DIALOG_TAG);
    }
  }

  private static void startRequestPermission(FragmentActivity activity,
    OnRequestResultListener onRequestResultListener, String[] permission) {
    XXPermissions.with(activity)
      .permission(permission)
      .request(new OnPermissionCallback() {
        @Override
        public void onGranted(List<String> permissions, boolean all) {
          if (onRequestResultListener != null) {
            onRequestResultListener.onRequestSuccess();
          }
        }

        @Override
        public void onDenied(List<String> permissions, boolean never) {
          XLog.d("onDenied:" + never);
          if (onRequestResultListener != null) {
            onRequestResultListener.onRequestFailed(permissions, never);
          }
        }
      });
  }

  public interface OnRequestResultListener {

    void onRequestSuccess();

    void onRequestFailed(List<String> permissions, boolean never);
  }
}
