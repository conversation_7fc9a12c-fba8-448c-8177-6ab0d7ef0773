<manifest package="com.bxkj.jrzp.live.api">
  <uses-permission android:name="android.permission.INTERNET" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.RECORD_AUDIO" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.CAMERA" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.READ_PHONE_STATE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-feature android:name="android.hardware.Camera" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-feature android:name="android.hardware.camera.autofocus" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <permission android:name="android.permission.BIND_JOB_SERVICE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.BLUETOOTH" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <application android:allowBackup="false" xmlns:android="http://schemas.android.com/apk/res/android" tools:replace="android:allowBackup" xmlns:tools="http://schemas.android.com/tools">
    <activity android:name="com.bxkj.jrzp.live.anchor.ui.anchor.LiveAnchorActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait" android:theme="@style/Live_AnchorActivityTheme"/>
    <activity android:name="com.bxkj.jrzp.live.room.ui.roomlist.LiveRoomListActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.live.room.ui.createroom.CreateLiveRoomActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.live.audience.ui.audience.LiveAudienceActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan"/>
    <activity android:name="com.bxkj.jrzp.live.room.ui.livenotice.LiveNoticeActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.live.anchor.ui.livestatistics.LiveStatisticsActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.live.notice.ui.list.LiveNoticeListActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.jrzp.live.videocall.ui.videocall.VideoCallActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <service android:name="com.bxkj.jrzp.live.videocall.ui.videocall.CallJobService" android:enabled="true" android:exported="false" android:permission="android.permission.BIND_JOB_SERVICE"/>
  </application>
</manifest>
