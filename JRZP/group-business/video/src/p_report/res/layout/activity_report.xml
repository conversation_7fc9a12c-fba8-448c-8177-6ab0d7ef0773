<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.report.ui.videoreport.VideoReportViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:background="@drawable/bg_f4f4f4"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:title="@string/report_title" />

    <androidx.core.widget.NestedScrollView style="@style/match_wrap">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <TextView
          style="@style/Text.16sp.333333"
          android:layout_marginStart="@dimen/dp_20"
          android:layout_marginTop="@dimen/dp_16"
          android:layout_marginEnd="@dimen/dp_20"
          android:layout_marginBottom="@dimen/dp_16"
          android:text="@string/report_tips" />

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_report_reason"
          style="@style/match_wrap"
          android:background="@drawable/bg_ffffff"
          android:overScrollMode="never"
          bind:items="@{viewModel.reportReasonItem}" />

        <EditText
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:layout_marginTop="@dimen/dp_20"
          android:background="@drawable/bg_ffffff_radius_4"
          android:gravity="top"
          android:hint="@string/report_hint"
          android:lines="5"
          android:padding="@dimen/dp_16"
          android:text="@={viewModel.otherReason}"
          android:visibility="@{viewModel.showOtherReason?View.VISIBLE:View.GONE}" />

        <TextView
          style="@style/Button.Basic"
          android:layout_marginStart="@dimen/dp_30"
          android:layout_marginTop="@dimen/dp_40"
          android:layout_marginEnd="@dimen/dp_30"
          android:onClick="@{onClickListener}"
          android:text="@string/report_btn_text" />
      </LinearLayout>
    </androidx.core.widget.NestedScrollView>
  </LinearLayout>
</layout>