package com.bxkj.video.repository

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.constants.AppConstants.UserType
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.support.upload.repository.UploadRepository
import com.bxkj.video.VideoLinkMethod
import com.bxkj.video.VideoSubType
import com.bxkj.video.VideoType
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.data.VideoData
import com.bxkj.video.openapi.OpenVideoApi
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/21
 * @version: V1.0
 */
class OpenVideoRepository @Inject constructor(
  private val mOpenVideoApi: OpenVideoApi,
  private val mUploadRepository: UploadRepository
) : BaseRepo() {

  /**
   * type
   * 1=个人用户
   * 2=企业用户
   * subType
   * 1=我发布的求职/招聘视频列表
   * 2=我收藏的视频列表
   * 3=我点赞的视频列表
   */
  suspend fun getUserVideoList(
    queryUserId: Int,
    myUserId: Int,
    @UserType queryUserType: Int,
    @VideoSubType.Type
    subType: Int,
    pageIndex: Int,
    pageSize: Int,
    px: Int = 0
  ): ReqResponse<List<OnlineVideoData>> {
    return httpRequest {
      mOpenVideoApi.getUserVideoList(
        ZPRequestBody()
          .apply {
            put("userID", queryUserId)
            put("myUserID", myUserId)
            put("type", queryUserType)
            put("subType", subType)
            put("pageIndex", pageIndex)
            put("pageSize", pageSize)
            put("px", px)
          }
      )
    }
  }

  suspend fun publishVideoByPath(
    userId: Int,
    @VideoType.Type type: Int,
    coverPath: String,
    videoPath: String,
    videoWidth: Int,
    videoHeight: Int
  ): ReqResponse<String> {
    var coverUrl: String?
    var videoUrl: String?
    mUploadRepository.uploadFile(
      coverPath,
      UploadFileRequestParams.fromFileType(userId, UploadFileRequestParams.TYPE_IMG)
    ).apply {
      if (this is ReqResponse.Success) {  //上传封面成功
        coverUrl = data
        mUploadRepository.uploadFile(
          videoPath,
          UploadFileRequestParams.fromFileType(userId, UploadFileRequestParams.TYPE_VIDEO)
        ).apply {
          return if (this is ReqResponse.Success) { //上传视频成功
            videoUrl = data
            publishVideo(
              userId, type, coverUrl ?: "", videoUrl
                ?: "", videoWidth, videoHeight
            )
          } else {
            this
          }
        }
      } else {
        return this
      }
    }
  }

  /**
   * 上传视频
   */
  private suspend fun publishVideo(
    userId: Int,
    @VideoType.Type type: Int,
    videoCover: String,
    videoUrl: String,
    videoWidth: Int,
    videoHeight: Int
  ): ReqResponse<String> {
    return httpRequest {
      mOpenVideoApi.publishVideo(
        ZPRequestBody()
          .apply {
            put("userID", userId)
            put("type", type)
            put("pic", videoCover)
            put("video", videoUrl)
            put("width", videoWidth)
            put("height", videoHeight)
          }
      ).apply {
        if (status == 10001) {
          data = msg
        }
      }
    }
  }

  /**
   * 获取信息关联视频
   */
  suspend fun getInfoLinkVideos(
    userId: Int,
    infoId: Int,
    @VideoType.Type infoType: Int
  ): ReqResponse<List<VideoData>> {
    return httpRequest {
      mOpenVideoApi.getInfoLinkVideos(
        ZPRequestBody().apply {
          put("userID", userId)
          put("id", infoId)
          put("type", infoType)
        })
    }
  }

  /**
   * 添加视频关联关系
   */
  suspend fun addVideoLinkInfo(
    userId: Int,
    cityId: Int,
    @VideoType.Type videoType: Int,
    @VideoLinkMethod.Method
    linkMethod: Int,
    videoIds: String,
    linkInfoIds: String,
    linkInfoTitles: String
  ): ReqResponse<Nothing> {
    return httpRequest {
      mOpenVideoApi.addVideoLinkInfo(
        ZPRequestBody()
          .apply {
            put("userID", userId)
            put("type", videoType)
            put("cityID", cityId)
            if (linkMethod == VideoLinkMethod.VIDEO_LINK_ONE_VIDEO_TO_MORE) {
              put("videoID", videoIds)
              put("otherIDs", linkInfoIds)
              put("otherNames", linkInfoTitles)
            } else {
              put("videoIDs", videoIds)
              put("otherID", linkInfoIds)
              put("otherName", linkInfoTitles)
            }
          }
      )
    }
  }

  /**
   * 清除视频关联关系
   */
  suspend fun removeVideoLinkInfo(
    userID: Int, @VideoLinkMethod.Method linkMethod: Int,
    @VideoType.Type videoType: Int, videoIds: String,
    infoIds: String
  ): ReqResponse<Nothing> {
    return httpRequest {
      mOpenVideoApi.removeVideoLinkInfo(
        ZPRequestBody()
          .apply {
            put("userID", userID)
            put("type", linkMethod)
            put("subType", videoType)
            if (linkMethod == VideoLinkMethod.VIDEO_LINK_ONE_VIDEO_TO_MORE) {
              put("id", videoIds)
              put("ids", infoIds)
            } else {
              put("id", infoIds)
              put("ids", videoIds)
            }
          }
      )
    }
  }

}