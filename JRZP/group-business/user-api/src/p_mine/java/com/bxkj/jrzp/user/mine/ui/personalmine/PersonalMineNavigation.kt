package com.bxkj.jrzp.user.mine.ui.personalmine

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/16
 * @version: V1.0
 */
object PersonalMineNavigation {

  const val PATH = "${UserConstants.DIRECTORY}/mine"

  fun navigate(): RouterNavigator {
    return Router.getInstance().to(PATH)
  }
}