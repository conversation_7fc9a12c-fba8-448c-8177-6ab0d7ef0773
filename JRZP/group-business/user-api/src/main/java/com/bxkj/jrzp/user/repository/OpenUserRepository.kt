package com.bxkj.jrzp.user.repository

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.constants.AppConstants.UserType
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.user.DeleteStep.Step
import com.bxkj.jrzp.user.FollowType
import com.bxkj.jrzp.user.LikeType.Type
import com.bxkj.jrzp.user.api.OpenUserApi
import com.bxkj.jrzp.user.api.OpenUserApiConstants.ContactType
import com.bxkj.jrzp.user.data.*
import com.bxkj.jrzp.user.mine.data.ServiceItemData
import com.bxkj.support.upload.repository.UploadRepository
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/31
 * @version: V1.0
 */
class OpenUserRepository @Inject constructor(
  private val mOpenUserApi: OpenUserApi,
  private val uploadRepository: UploadRepository
) : BaseRepo() {

  /**
   * 获取用户绑定手机号
   */
  suspend fun getUserBindPhoneNumber(): ReqResponse<String> {
    return httpRequest {
      mOpenUserApi.getUserBindPhoneNumber(ZPRequestBody()).apply {
        if (requestSuccess()) {
          data = msg
        }
      }
    }
  }

  /**
   * 修改微信号
   */
  suspend fun updateUserWechat(wechatNumber: String): ReqResponse<Nothing> {
    return httpRequest {
      mOpenUserApi.updateUserWechat(
        ZPRequestBody().apply {
          put("weixin", wechatNumber)
        }
      )
    }
  }

  /**
   * 修改用户联系方式
   */
  suspend fun updateUserContactPhone(phoneNumber: String, smsCode: String): ReqResponse<Nothing> {
    return httpRequest {
      mOpenUserApi.updateUserContactPhone(
        ZPRequestBody().apply {
          put("phone", phoneNumber)
          put("smsCode", smsCode)
        }
      )
    }
  }

  /**
   * 获取用户联系方式
   */
  suspend fun getUserContactInfo(contactType: ContactType): ReqResponse<UserContactWayBean> {
    return httpRequest {
      mOpenUserApi.getUserContactWay(
        ZPRequestBody().apply {
          put("comType", contactType.value)
        }
      )
    }
  }

  /**
   * 获取简历分数
   */
  suspend fun getResumeScore(): ReqResponse<ResumeScoreData> {
    return httpRequest {
      mOpenUserApi.getResumeScore()
    }
  }

  /**
   * 上传公司头像
   */
  suspend fun uploadCompanyLogo(userId: Int, imgPath: String): ReqResponse<Nothing> {
    val uploadImgResult = uploadRepository.uploadFileV3(
      imgPath,
      UploadFileRequestParams.getImageUploadParams(
        userId,
        UploadFileRequestParams.PATH_NAME_COMPANY_LOGO
      )
    )
    if (uploadImgResult is ReqResponse.Success) {
      uploadImgResult.data?.let {
        return httpRequest {
          mOpenUserApi.uploadCompanyLogo(
            ZPRequestBody().apply {
              put("uid", userId)
              put("type", 1)
              put("picId", it.id)
              put("logo", it.url)
            }.paramsEncrypt()
          )
        }
      } ?: let { return ReqResponse.Failure(RespondThrowable.getDefault()) }
    } else {
      return ReqResponse.Failure(RespondThrowable.getDefault())
    }
  }

  /**
   * 发布宣讲会预校验[type] 0:宣讲会1:云宣讲
   */
  suspend fun releaseCampusTalkPreCheck(type: Int): ReqResponse<CampusTalkCountData> {
    return httpRequest {
      mOpenUserApi.releaseCampusTalkPreCheck(
        ZPRequestBody().apply {
          put("ntid", type)
        }.paramsEncrypt()
      ).apply {
        if (status == 10001) {
          data.type = 0
        } else if (status == 10002) {
          status = 10001
          data.type = 1
        }
      }
    }
  }

  /**
   * 获取客服信息
   */
  suspend fun getServicesInfo(): ReqResponse<ServiceItemData> {
    return httpRequest {
      mOpenUserApi.getServicesInfo()
    }
  }

  /**
   * 获取用户客服信息
   */
  suspend fun getUserServicesInfo(): ReqResponse<List<ServiceItemData>> {
    return httpRequest {
      mOpenUserApi.getUserServicesInfo()
    }
  }

  suspend fun like(
    userID: Int,
    @Type likeType: Int,
    likeID: Int,
    @UserType userType: Int
  ): ReqResponse<Nothing> {
    return httpRequest {
      mOpenUserApi.like(
        ZPRequestBody().apply {
          put("type", likeType)
          put("uid", userID)
          put("nid", likeID)
          put("userType", userType)
        }
      )
    }
  }

  suspend fun follow(
    selfUserID: Int,
    @FollowType.Type followType: Int,
    followId: Int
  ): ReqResponse<Nothing> {
    return httpRequest {
      mOpenUserApi.follow(
        ZPRequestBody().apply {
          put("uid", selfUserID)
          put("gzType", followType)
          put("glID", followId)
        }
      )
    }
  }

  suspend fun checkEnterpriseAuthHasComplete(userId: Int): ReqResponse<Nothing> {
    return httpRequest {
      mOpenUserApi.checkEnterpriseAuthHasComplete(
        ZPRequestBody().apply {
          put("userID", userId)
        })
    }
  }

  suspend fun deleteReleasedInfo(
    userId: Int,
    deleteType: Int,
    deleteId: Int,
    @Step deleteStep: Int = 0
  ): ReqResponse<String> {
    return httpRequest {
      mOpenUserApi.deleteReleasedInfo(
        ZPRequestBody().apply {
          put("uid", userId)
          put("type", deleteType)
          put("id", deleteId)
          put("isTips", deleteStep)
        }.paramsEncrypt()
      ).apply {
        if (requestSuccess()) {
          data = msg
        }
      }
    }
  }

  /**
   * 获取腾讯IM SignID
   */
  suspend fun getTencentIMSigID(userId: String): ReqResponse<String> {
    return httpRequest {
      mOpenUserApi.getTencentIMSigID(
        ZPRequestBody().apply {
          put("uid", userId)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取用户会员级别
   */
  suspend fun getUserVipLevel(userID: Int): ReqResponse<UserVipLevelData> {
    return httpRequest {
      mOpenUserApi.getUserVipLevel(
        ZPRequestBody().apply {
          put("uid", userID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取双选会企业校招只职位列表
   */
  suspend fun getDoubleElectionCompanyJobList(
    selfUserID: Int,
    companyID: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<JobData>> {
    return httpRequest {
      mOpenUserApi.getCompanySchoolJobList(
        ZPRequestBody().apply {
          put("curUid", selfUserID)
          put("uid", companyID)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取用户剩余邀约数
   */
  suspend fun getUserInviteBalance(userID: Int): ReqResponse<UserVipBalanceData> {
    return httpRequest {
      mOpenUserApi.getUserInviteBalance(
        ZPRequestBody().apply {
          put("uid", userID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取用户剩余打招呼数
   */
  suspend fun getSayHelloBalance(userID: Int): ReqResponse<UserVipBalanceData> {
    return httpRequest {
      mOpenUserApi.getSayHelloBalance(
        ZPRequestBody().apply {
          put("uid", userID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取用户VIP信息账户
   */
  suspend fun getEnterpriseVipAccountBalance(userId: Int): ReqResponse<AccountVipData> {
    return httpRequest {
      mOpenUserApi.getEnterpriseAccountBalance(
        ZPRequestBody().apply {
          put("uid", userId)
        }.paramsEncrypt()
      )
    }
  }
}