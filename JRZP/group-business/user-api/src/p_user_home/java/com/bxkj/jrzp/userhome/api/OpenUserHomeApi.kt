package com.bxkj.jrzp.userhome.api

import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.jrzp.user.mine.data.UserHomeData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/24
 * @version: V1.0
 */
interface OpenUserHomeApi{

  @POST("/User/GetUserPageInfoV2/")
  suspend fun getUserHomePageInfo(@Body mRequestBody: ZPRequestBody): BaseResponse<UserHomeData>
}