package com.bxkj.jrzp.userhome.api

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.user.api.OpenAuthenticationApi
import com.bxkj.jrzp.user.data.UserAuthStatusData
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType.Type
import com.bxkj.jrzp.user.mine.data.UserHomeData
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/24
 * @version: V1.0
 */
class OpenUserHomeRepository @Inject constructor(
  private val mOpenUserHomeApi: OpenUserHomeApi,
  private val mOpenAuthenticationApi: OpenAuthenticationApi
) : BaseRepo() {

  /**
   * 获取用户主页信息
   */
  suspend fun getUserHomePageInfo(
    queryId: Int,
    @Type queryUserAuthType: Int,
    myUserId: Int,
    queryEnterpriseId: Int = 0
  ): ReqResponse<UserHomeData> {
    return httpRequest {
      if (queryUserAuthType == AuthenticationType.QUERY_HIGHER_AUTH) {
        var authStatus = UserAuthStatusData.getDefault()
        mOpenAuthenticationApi.getUserAuthStatus(
          ZPRequestBody().apply {
            put("uid", queryId)
          }.paramsEncrypt()
        ).let {
          if (it.requestSuccess()) {
            kotlin.run breaking@{
              it.data.forEach { item ->
                if (item.authSuccess()) {
                  authStatus = item
                  return@breaking
                }
              }
            }
          }
          getUserInfo(queryId, queryEnterpriseId, myUserId, authStatus)
        }
      } else {
        getUserInfo(
          queryId,
          queryEnterpriseId,
          myUserId,
          UserAuthStatusData.getSuccessStatus(queryUserAuthType)
        )
      }
    }
  }

  private suspend fun getUserInfo(
    queryUserId: Int,
    queryInstitutionsID: Int,
    myUserId: Int,
    authState: UserAuthStatusData
  ): BaseResponse<UserHomeData> {
    return mOpenUserHomeApi.getUserHomePageInfo(
      ZPRequestBody().apply {
        put("userID", queryUserId)
        put("dwID", queryInstitutionsID)
        put("myUserID", myUserId)
        put("rzType", authState.type)
      }
    ).apply {
      if (requestSuccess()) {
        data.authStatus = authState
      }
    }
  }
}