package com.bxkj.jrzp.live.di

import com.bxkj.jrzp.live.api.LiveApi
import com.bxkj.jrzp.live.api.OpenLiveApi
import com.bxkj.jrzp.live.room.api.LiveRoomApi
import com.bxkj.jrzp.live.room.api.OpenLiveRoomApi
import dagger.Module
import dagger.Provides
import retrofit2.Retrofit

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/15
 * @version: V1.0
 */
@Module
class LiveApiModule {

  @Provides
  fun provideLiveRoomApi(retrofit: Retrofit): LiveRoomApi {
    return retrofit.create(LiveRoomApi::class.java)
  }

  @Provides
  fun provideOpenLiveRoomApi(retrofit: Retrofit): OpenLiveRoomApi {
    return retrofit.create(OpenLiveRoomApi::class.java)
  }

  @Provides
  fun provideLiveApi(retrofit: Retrofit): LiveApi {
    return retrofit.create(LiveApi::class.java)
  }

  @Provides
  fun provideOpenLiveApi(retrofit: Retrofit): OpenLiveApi {
    return retrofit.create(OpenLiveApi::class.java)
  }
}