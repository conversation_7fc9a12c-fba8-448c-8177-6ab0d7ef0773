package com.bxkj.jrzp.user.switchaccount.ui.switchaccount

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.jrzp.support.db.AppDatabase
import com.bxkj.jrzp.support.db.entry.UserLoginHistory
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType
import com.bxkj.jrzp.user.repository.UserRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/19
 * @version: V1.0
 */
class SwitchAccountViewModel @Inject constructor(
  private val mAppDatabase: AppDatabase,
  private val mUserRepository: UserRepository
) : BaseViewModel() {

  val userAccountList = MutableLiveData<ArrayList<UserLoginHistory>>()

  val openEdit = MutableLiveData<Boolean>().apply { value = false }

  fun start() {
    userAccountList.value = ArrayList(mAppDatabase.userLoginHistoryDao().getUserLoginHistory())
  }

  fun switchEditStatus() {
    openEdit.value?.let {
      openEdit.value = it.not()
    }
  }

  fun deleteLoginHistory(userLoginHistory: UserLoginHistory) {
    if (userLoginHistory.userID == getSelfUserID()) {
      return
    }
    mAppDatabase.userLoginHistoryDao().deleteHistory(userLoginHistory.userID)
    start()
  }

  fun switchAccount(clickItem: UserLoginHistory) {
    showToast(R.string.user_switch_account_success)
    UserUtils.saveUserData(clickItem.userID, getUserIdentity(clickItem))
    UserUtils.saveUserToken(clickItem.token)
    viewModelScope.launch {
      mUserRepository.bindPushToken(getSelfUserID(), UserUtils.getPushToken())
    }
    RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_LOGIN_SUCCESS))
  }

  private fun getUserIdentity(clickItem: UserLoginHistory): Int {
    return if (clickItem.identity == 0) {
      AuthenticationType.PERSONAL
    } else {
      clickItem.identity
    }
  }
}