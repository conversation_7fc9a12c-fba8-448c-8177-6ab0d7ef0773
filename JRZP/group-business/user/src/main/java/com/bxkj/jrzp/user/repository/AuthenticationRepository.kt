package com.bxkj.jrzp.user.repository

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.user.api.AuthenticationApi
import com.bxkj.jrzp.user.data.AuthenticationInfoData
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType.Type
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/14
 * @version: V1.0
 */
class AuthenticationRepository @Inject constructor(
  private val mAuthenticationApi: AuthenticationApi
) : BaseRepo() {

  /**
   * 提交认证
   */
  suspend fun submitAuthentication(
    userId: Int,
    name: String,
    pics: String,
    picIds: String,
    @Type authenticationType: Int
  ): ReqResponse<Nothing> {
    return httpRequest {
      mAuthenticationApi.submitAuthenticationV3(
        ZPRequestBody().apply {
          put("uid", userId)
          put("name", name)
          put("pic", pics)
          put("picId", picIds)
          put("type", authenticationType)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取认证信息
   */
  suspend fun getUserAuthenticationInfo(userId: Int): ReqResponse<List<AuthenticationInfoData>> {
    return httpRequest {
      mAuthenticationApi.getUserAuthenticationInfo(
        ZPRequestBody().apply {
          put("uid", userId)
        }.paramsEncrypt()
      )
    }
  }

}