package com.bxkj.jrzp.user.data

import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2020/1/4
 */
data class AuthenticationInfoData
  (
  /**
   * id : 28517
   * uid : 0
   * cid : 0
   * name : 刘双锋
   * no : **********
   * pic : images_server/businessLicense/104164/20180223114751446.png
   * sdate : 2018/2/1 0:00:00
   * edate : 2020/2/1 0:00:00
   * status : 2
   * shResult :
   */
  var type: Int = AuthenticationType.ENTERPRISE,
  var name: String = "",
  var status: Int = 0,
  var shResult: String? = null,
  var picDomain: String? = null,
  var picList: List<AuthPic>? = null
) {

  data class AuthPic(
    var pic: String?
  )

}