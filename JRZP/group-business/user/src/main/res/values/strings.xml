<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">

  <!--=============IdCardValidationActivity(身份验证)============-->
  <string name="user_idcard_validation_page_title">实名认证</string>
  <string name="user_idcard_validation_tips">发布职位前需先完成身份认证</string>
  <string name="user_idcard_validation_name">姓名</string>
  <string name="user_idcard_validation_name_hint">输入姓名</string>
  <string name="user_idcard_validation_name_note">姓名应与身份证一致</string>
  <string name="user_idcard_validation_id_number">身份证号</string>
  <string name="user_idcard_validation_id_number_hint">输入身份证号</string>
  <string name="user_idcard_validation_id_number_note">请输入18位身份证号码</string>
  <string name="user_idcard_validation_id_desc">简介</string>
  <string name="user_idcard_validation_id_desc_hint">简要概述个人身份</string>
  <string name="user_idcard_validation_submit">提交</string>

  <!--=============AlipayAccountListActivity(支付宝账户列表)============-->
  <string name="user_alipay_account_list_page_title">选择支付宝账户</string>

  <!--=============AddBankAccountActivity(添加银行账户)============-->
  <string name="user_add_bank_account_page_title">银行账户</string>
  <string name="user_add_bank_account_submit">提交</string>
  <string name="user_add_bank_account_name">姓名</string>
  <string name="user_add_bank_account_name_hint">输入姓名</string>
  <string name="user_add_bank_account_title1">开户行</string>
  <string name="user_add_bank_account_title1_hint">输入开户行</string>
  <string name="user_add_bank_account_title2">卡号</string>
  <string name="user_add_bank_account_title2_hint">输入卡号</string>

  <!--SelectAuthTypeActivity(选择认证类型)-->
  <string name="user_select_auth_type_page_title">今日招聘用户认证</string>
  <string name="user_select_auth_personal">个人实名认证</string>
  <string name="user_select_auth_enterprise">企业加V认证</string>
  <string name="user_select_auth_confirm_format">您已选择%s\n一旦提交认证资料，您将无法进行其他类型的认证</string>

  <!--PersonalAuthenticationActivity(个人认证)-->
  <string name="user_personal_authentication_page_title">实名认证</string>
  <string name="user_personal_authentication_tips">根据互联网相关条例必须做以下实名认证</string>
  <string name="user_personal_authentication_step_one_tips_1">请拍摄并上传身份证</string>
  <string name="user_personal_authentication_step_one_tips_2">拍摄照片要求：</string>
  <string name="user_personal_authentication_step_two_tips_1">请拍摄并上传您的手持身份证照片</string>
  <string name="user_personal_authentication_next_step">下一步</string>
  <string name="user_personal_authentication_complete">完成</string>
  <string name="user_personal_authentication_submit_success_tips_title">认证提交成功</string>
  <string name="user_personal_authentication_submit_success_tips_content">审核结果将于1个工作日内短信通知您</string>

  <!--JobProveAuthActivity(在职证明)-->
  <string name="user_job_prove_auth_page_title">在职证明</string>
  <string name="user_job_prove_auth_tips">示范案例：</string>
  <string name="user_job_prove_auth_complete">完成</string>

</resources>