<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.jrzp.user.ui.selectauthtype.AuthTypeItem" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap">

    <ImageView
      android:id="@+id/iv_auth_icon"
      android:layout_width="@dimen/common_dp_28"
      android:layout_height="@dimen/common_dp_28"
      android:layout_marginStart="@dimen/dp_24"
      bind:imgUrl="@{data.authIcon}"
      app:layout_constraintBottom_toBottomOf="@id/tv_auth_desc"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_auth_title" />

    <TextView
      android:id="@+id/tv_auth_title"
      style="@style/Text.14sp.333333.Bold"
      android:layout_marginStart="@dimen/dp_18"
      android:layout_marginTop="@dimen/dp_8"
      android:text="@{data.authTitle}"
      app:layout_constraintStart_toEndOf="@id/iv_auth_icon"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_auth_desc"
      style="@style/Text.13sp.999999"
      android:layout_width="@dimen/dp_0"
      android:layout_marginEnd="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_22"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.authDesc}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toStartOf="@id/iv_next"
      app:layout_constraintStart_toStartOf="@id/tv_auth_title"
      app:layout_constraintTop_toBottomOf="@id/tv_auth_title" />

    <ImageView
      android:id="@+id/iv_next"
      style="@style/wrap_wrap"
      android:layout_marginEnd="@dimen/dp_14"
      android:src="@drawable/ic_small_more"
      app:layout_constraintBottom_toBottomOf="@id/tv_auth_desc"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_auth_title" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>