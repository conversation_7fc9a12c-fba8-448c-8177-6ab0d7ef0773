<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match">

    <com.bxkj.common.widget.MyVideoPlayer
        android:id="@+id/video_player"
        style="@style/match_match"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_bottom_tag"
        style="@style/match_wrap"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_play_count"
            style="@style/Text.12sp.FFFFFF"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_review_status"
            style="@style/Text.12sp.FF7647"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_video_sign_up"
            style="@style/Text.10sp.FFFFFF"
            android:layout_marginEnd="@dimen/dp_8"
            android:background="@color/common_fa3b3a"
            android:paddingStart="@dimen/dp_8"
            android:paddingTop="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_8"
            android:paddingBottom="@dimen/dp_2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_tag"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_0" />

        <TextView
            android:id="@+id/tv_tag"
            style="@style/Text.10sp.FFFFFF"
            android:background="@color/cl_ff7405"
            android:paddingStart="@dimen/dp_4"
            android:paddingTop="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>