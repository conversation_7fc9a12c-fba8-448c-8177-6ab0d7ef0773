<?xml version="1.0" encoding="utf-8"?>
<LinearLayout style="@style/match_match"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@drawable/bg_ffffff"
    android:orientation="vertical">

    <include layout="@layout/common_include_title_bar" />

    <androidx.core.widget.NestedScrollView style="@style/match_wrap">

        <LinearLayout
            style="@style/match_wrap"
            android:orientation="vertical">

            <TextView
                style="@style/Text.InputTips"
                android:text="@string/personal_required_text" />

            <LinearLayout style="@style/Layout.ItemInfo">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/personal_start_time" />

                <TextView
                    android:id="@+id/tv_start_time"
                    style="@style/Text.InfoItem"
                    android:drawableEnd="@drawable/common_ic_next"
                    android:drawablePadding="@dimen/dp_4"
                    android:hint="@string/common_please_select" />
            </LinearLayout>

            <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout style="@style/Layout.ItemInfo">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/personal_end_time" />

                <TextView
                    android:id="@+id/tv_end_time"
                    style="@style/Text.InfoItem"
                    android:drawableEnd="@drawable/common_ic_next"
                    android:drawablePadding="@dimen/dp_4"
                    android:hint="@string/common_please_select" />
            </LinearLayout>

            <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout style="@style/Layout.ItemInfo">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/personal_company_name" />

                <com.bxkj.common.widget.MyEditText
                    android:id="@+id/et_company_name"
                    style="@style/EditText.PublishItem"
                    android:hint="@string/please_enter" />
            </LinearLayout>

            <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout style="@style/Layout.ItemInfo">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/personal_position_name" />

                <com.bxkj.common.widget.MyEditText
                    android:id="@+id/et_position_name"
                    style="@style/EditText.PublishItem"
                    android:hint="@string/please_enter"
                    android:imeOptions="actionDone" />
            </LinearLayout>

            <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout style="@style/Layout.ItemInfo">

                <TextView
                    style="@style/Text.15sp.333333"
                    android:text="@string/personal_work_desc" />

                <TextView
                    android:id="@+id/tv_work_desc"
                    style="@style/Text.InfoItem"
                    android:hint="@string/please_enter" />
            </LinearLayout>

            <View
                style="@style/Line.Horizontal.Margin12OfStartAndEnd"
                android:layout_marginBottom="@dimen/dp_30" />

            <TextView
                android:id="@+id/tv_delete_this"
                style="@style/Text.DeleteResumeItem"
                android:text="@string/personal_delete_this_work_exp" />
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>