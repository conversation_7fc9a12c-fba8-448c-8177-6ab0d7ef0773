<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.jobdetails.JobDetailsViewModelV2" />
  </data>

  <LinearLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44"
      android:background="@drawable/bg_ffffff">

      <ImageView
        android:id="@+id/iv_back"
        style="@style/wrap_wrap"
        android:onClick="@{onClickListener}"
        android:src="@drawable/common_ic_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_title"
        style="@style/Text.18sp.333333.Bold"
        android:layout_width="@dimen/dp_0"
        android:layout_marginStart="100dp"
        android:layout_marginEnd="100dp"
        android:ellipsize="marquee"
        android:gravity="center"
        android:singleLine="true"
        android:text="@string/job_details_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <ImageView
        android:id="@+id/iv_option_one"
        style="@style/wrap_wrap"
        android:minWidth="@dimen/dp_36"
        android:minHeight="@dimen/dp_36"
        android:onClick="@{onClickListener}"
        android:scaleType="centerInside"
        android:src="@drawable/live_ic_user_home_report"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_option_two"
        app:layout_constraintTop_toTopOf="parent" />

      <ImageView
        android:id="@+id/iv_option_two"
        style="@style/wrap_wrap"
        android:minWidth="@dimen/dp_36"
        android:minHeight="@dimen/dp_36"
        android:onClick="@{()->viewModel.toggleFavorite()}"
        android:scaleType="centerInside"
        android:src="@{viewModel.jobIsCollected?@drawable/ic_job_collection_sel:@drawable/ic_job_collection_nor}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_option_three"
        app:layout_constraintTop_toTopOf="parent" />

      <ImageView
        android:id="@+id/iv_option_three"
        style="@style/wrap_wrap"
        android:minWidth="@dimen/dp_36"
        android:minHeight="@dimen/dp_36"
        android:onClick="@{onClickListener}"
        android:scaleType="centerInside"
        android:src="@drawable/ic_news_details_share"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
      android:id="@+id/ll_content"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:orientation="vertical">

      <com.donkingliang.consecutivescroller.ConsecutiveScrollerLayout
        android:id="@+id/scroll_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_weight="1">

        <androidx.constraintlayout.widget.ConstraintLayout
          android:id="@+id/cl_job_basic_info"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:background="@drawable/bg_ffffff">

          <com.bxkj.common.widget.ZPTextView
            android:id="@+id/tv_job_name"
            style="@style/Text.20sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_12"
            android:text="@{HtmlUtils.fromHtml(@string/c_job_details_name_and_salary_format(viewModel.jobDetails.name,viewModel.jobDetails.covertSalary))}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_0" />

          <TextView
            android:id="@+id/tv_job_address"
            style="@style/Text.14sp.888888.SingleLine"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_16"
            android:drawableStart="@drawable/ic_address"
            android:drawablePadding="@dimen/common_dp_5"
            android:text="@{viewModel.jobDetails.xianName}"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.jobDetails.xianName) ? View.GONE : View.VISIBLE}"
            app:layout_constraintEnd_toStartOf="@id/tv_job_exp"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name" />

          <TextView
            android:id="@+id/tv_job_exp"
            style="@style/Text.14sp.888888.SingleLine"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_16"
            android:drawableStart="@drawable/personal_ic_job_exp"
            android:drawablePadding="@dimen/common_dp_5"
            android:text="@{viewModel.jobDetails.wtName}"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.jobDetails.wtName) ? View.GONE : View.VISIBLE}"
            app:layout_constraintEnd_toStartOf="@id/tv_job_education"
            app:layout_constraintStart_toEndOf="@id/tv_job_address"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name"
            app:layout_goneMarginStart="@dimen/dp_12" />

          <TextView
            android:id="@+id/tv_job_education"
            style="@style/Text.14sp.888888.SingleLine"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_16"
            android:drawableStart="@drawable/personal_ic_job_education"
            android:drawablePadding="@dimen/common_dp_5"
            android:text="@{viewModel.jobDetails.quaName}"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.jobDetails.quaName) ? View.GONE : View.VISIBLE}"
            app:layout_constraintEnd_toStartOf="@id/tv_job_nature"
            app:layout_constraintStart_toEndOf="@id/tv_job_exp"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name"
            app:layout_goneMarginStart="@dimen/dp_4" />

          <TextView
            android:id="@+id/tv_job_nature"
            style="@style/common_Text.14sp.888888.SingleLine"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_16"
            android:drawableStart="@drawable/ic_job_nature"
            android:drawablePadding="@dimen/common_dp_5"
            android:gravity="center_vertical"
            android:text="@{viewModel.jobDetails.nature}"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.jobDetails.nature) ? View.GONE : View.VISIBLE}"
            app:layout_constraintEnd_toStartOf="@id/tv_identity_require"
            app:layout_constraintStart_toEndOf="@id/tv_job_education"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name" />

          <TextView
            android:id="@+id/tv_identity_require"
            style="@style/common_Text.14sp.888888.SingleLine"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_12"
            android:drawableStart="@drawable/ic_identity_student"
            android:drawablePadding="@dimen/common_dp_5"
            android:gravity="center_vertical"
            android:text="@{viewModel.jobDetails.identityRequireText}"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.jobDetails.identityRequireText) ? View.GONE : View.VISIBLE}"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_job_nature"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name" />

          <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_job_info_bottom"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_job_address,tv_job_exp,tv_job_education,tv_job_nature,tv_identity_require" />

          <com.bxkj.common.widget.labellayout.LabelLayout
            android:id="@+id/ll_welfare"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_12"
            app:itemBackground="@drawable/bg_f4f4f4_radius_2"
            app:itemMargin="@dimen/dp_4"
            app:itemPaddingLR="@dimen/dp_6"
            app:itemPaddingTB="@dimen/dp_2"
            app:itemTextColor="@color/cl_333333"
            app:itemTextSize="@dimen/dp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barrier_job_info_bottom"
            bind:items="@{viewModel.jobDetails.welfareTextArray}" />

          <!--        <androidx.recyclerview.widget.RecyclerView-->
          <!--          android:id="@+id/recycler_welfare"-->
          <!--          style="@style/wrap_wrap"-->
          <!--          android:layout_width="@dimen/dp_0"-->
          <!--          android:layout_marginStart="@dimen/dp_12"-->
          <!--          android:layout_marginTop="@dimen/dp_8"-->
          <!--          android:layout_marginEnd="@dimen/dp_12"-->
          <!--          app:layout_constraintEnd_toEndOf="parent"-->
          <!--          app:layout_constraintHorizontal_bias="0"-->
          <!--          app:layout_constraintStart_toStartOf="parent"-->
          <!--          app:layout_constraintTop_toBottomOf="@id/tv_job_address" />-->

          <View
            style="@style/Line.Horizontal.Light"
            android:layout_height="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_welfare"
            app:layout_goneMarginTop="@dimen/dp_16" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--公司描述-->
        <androidx.constraintlayout.widget.ConstraintLayout
          android:id="@+id/cl_company_info"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:background="@drawable/bg_ffffff"
          android:onClick="@{()->viewModel.jumpToCompanyHomePage()}"
          android:paddingStart="@dimen/dp_12"
          android:paddingTop="@dimen/dp_16"
          android:paddingEnd="@dimen/dp_12"
          android:paddingBottom="@dimen/dp_16">

          <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_company_logo"
            style="@style/roundedCornerImageStyle.Avatar"
            android:layout_width="@dimen/dp_52"
            android:layout_height="@dimen/dp_52"
            android:background="@drawable/frame_eaeaea_round"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:imgUrl="@{viewModel.jobDetails.com.logo}" />

          <TextView
            android:id="@+id/tv_company_name"
            style="@style/Text.17sp.000000"
            android:layout_width="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_12"
            android:text="@{viewModel.jobDetails.com.name}"
            app:layout_constraintEnd_toStartOf="@id/iv_next"
            app:layout_constraintStart_toEndOf="@id/iv_company_logo"
            app:layout_constraintTop_toTopOf="parent" />

          <TextView
            android:id="@+id/tv_company_about"
            style="@style/Text.14sp.888888"
            android:layout_width="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/common_dp_5"
            android:layout_marginEnd="@dimen/dp_12"
            android:text="@{viewModel.jobDetails.com.about}"
            app:layout_constraintEnd_toStartOf="@id/iv_next"
            app:layout_constraintStart_toEndOf="@id/iv_company_logo"
            app:layout_constraintTop_toBottomOf="@id/tv_company_name" />

          <ImageView
            android:id="@+id/iv_next"
            style="@style/wrap_wrap"
            android:src="@drawable/common_ic_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_height="@dimen/dp_8" />

        <!--职位描述-->
        <LinearLayout
          android:id="@+id/ll_job_desc"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:background="@drawable/bg_ffffff"
          android:orientation="vertical">

          <com.bxkj.common.widget.ZPTextView
            android:id="@+id/tv_desc_of_content"
            style="@style/Text.JobDetailsItemTitle"
            android:text="@string/personal_desc_of_job" />

          <TextView
            android:id="@+id/tv_desc_of_text"
            style="@style/Text.16sp.767676"
            android:layout_width="match_parent"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_18"
            android:layout_marginEnd="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_16"
            android:lineSpacingExtra="@dimen/dp_4"
            android:text="@{HtmlUtils.fromHtml(viewModel.jobDetails.des)}" />
        </LinearLayout>

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_height="@dimen/dp_8" />

        <!--      地理位置-->
        <androidx.constraintlayout.widget.ConstraintLayout
          style="@style/match_wrap"
          android:background="@drawable/bg_ffffff">

          <com.bxkj.common.widget.ZPTextView
            android:id="@+id/tv_work_place"
            style="@style/Text.JobDetailsItemTitle"
            android:text="@string/personal_working_place"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

          <TextView
            android:id="@+id/tv_see_directions"
            style="@style/Text.12sp.FF7647"
            android:layout_height="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_12"
            android:background="@drawable/common_bg_rounded_rectangle_f8f8f8"
            android:drawableEnd="@drawable/personal_ic_job_desc_contract_see_derictions"
            android:drawablePadding="@dimen/dp_8"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_10"
            android:paddingEnd="@dimen/dp_10"
            android:text="@string/personal_see_directions"
            app:layout_constraintBottom_toBottomOf="@id/tv_work_place"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_work_place" />

          <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/fl_company_location"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/img_map_placeholder"
            app:layout_constraintDimensionRatio="25:9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_see_directions">

            <com.baidu.mapapi.map.TextureMapView
              android:id="@+id/map_view"
              android:layout_width="0dp"
              android:layout_height="0dp"
              app:layout_constraintBottom_toBottomOf="parent"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toTopOf="parent" />

            <ImageView
              android:id="@+id/iv_oval"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:src="@drawable/personal_ic_map_oval"
              app:layout_constraintBottom_toBottomOf="parent"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toTopOf="parent" />

            <TextView
              android:id="@+id/tv_map_address"
              style="@style/Text.14sp.333333"
              android:layout_marginStart="@dimen/dp_24"
              android:layout_marginEnd="@dimen/dp_24"
              android:layout_marginBottom="@dimen/dp_4"
              android:background="@drawable/bg_ffffff"
              android:ellipsize="end"
              android:lineSpacingExtra="@dimen/dp_3"
              android:maxLines="2"
              android:paddingStart="@dimen/dp_12"
              android:paddingTop="@dimen/dp_6"
              android:paddingEnd="@dimen/dp_12"
              android:paddingBottom="@dimen/dp_6"
              android:text="@{viewModel.jobDetails.fixedAddress}"
              app:layout_constraintBottom_toTopOf="@id/iv_oval"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent" />
          </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
          style="@style/Text.15sp.888888"
          android:drawableStart="@drawable/personal_ic_job_desc_tips"
          android:drawablePadding="@dimen/dp_16"
          android:gravity="center_vertical"
          android:lineSpacingExtra="@dimen/dp_3"
          android:paddingStart="@dimen/dp_12"
          android:paddingTop="@dimen/dp_16"
          android:paddingEnd="@dimen/dp_12"
          android:paddingBottom="@dimen/dp_16"
          android:text="@string/personal_job_desc_tips"
          app:layout_constraintTop_toBottomOf="@id/cl_content" />

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_height="@dimen/dp_8" />

        <com.bxkj.common.widget.ZPTextView
          style="@style/Text.JobDetailsItemTitle"
          android:paddingTop="@dimen/dp_16"
          android:text="@string/c_job_details_recommend_job" />

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_job_list"
          android:layout_width="match_parent"
          android:layout_height="wrap_content" />

      </com.donkingliang.consecutivescroller.ConsecutiveScrollerLayout>

      <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_52"
        android:background="@color/common_fbfbfb">

        <View
          android:id="@+id/v_top_line"
          style="@style/Line.Horizontal"
          android:background="@color/common_f0f0f0"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent" />

        <TextView
          android:id="@+id/tv_chat"
          style="@style/CheckBox.JobDetailsBottom"
          android:drawableTop="@drawable/ic_job_details_chat"
          android:enabled="@{viewModel.jobDetails!=null}"
          android:onClick="@{()->viewModel.toConversation()}"
          android:text="@{viewModel.hasConversation?@string/job_details_conversation:@string/job_details_see_hello}"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent" />

        <TextView
          android:id="@+id/tv_submit"
          style="@style/Button.Basic"
          android:layout_width="@dimen/dp_0"
          android:layout_height="@dimen/common_dp_42"
          android:layout_marginStart="@dimen/dp_24"
          android:enabled="@{viewModel.jobDetails!=null}"
          android:onClick="@{()->viewModel.callCompanyPreCheck()}"
          android:text="@string/job_details_contract"
          android:visibility="@{viewModel.hasContractWay?View.VISIBLE:View.GONE}"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintEnd_toStartOf="@id/tv_delivery"
          app:layout_constraintStart_toEndOf="@id/tv_chat"
          app:layout_constraintTop_toTopOf="parent" />

        <TextView
          android:id="@+id/tv_delivery"
          style="@style/Button.Basic"
          android:layout_width="@dimen/dp_0"
          android:layout_height="@dimen/common_dp_42"
          android:layout_marginStart="@dimen/dp_12"
          android:layout_marginEnd="@dimen/dp_12"
          android:enabled="@{viewModel.jobDetails!=null&amp;&amp;(!viewModel.submittedResume)}"
          android:onClick="@{()->viewModel.toSendResume()}"
          android:text="@{viewModel.submittedResume?@string/job_details_submitted:@string/job_details_delivery}"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toEndOf="@id/tv_submit"
          app:layout_constraintTop_toTopOf="parent" />

      </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
  </LinearLayout>
</layout>
