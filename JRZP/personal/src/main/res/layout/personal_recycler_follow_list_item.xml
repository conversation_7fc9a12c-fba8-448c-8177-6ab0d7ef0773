<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.personal.data.FollowItemData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_14"
    android:paddingTop="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_14">

    <FrameLayout
      android:id="@+id/fl_avatar"
      style="@style/wrap_wrap"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent">

      <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/common_dp_64"
        android:layout_height="@dimen/common_dp_64"
        android:background="@drawable/frame_f4f4f4_round"
        android:padding="@dimen/dp_1"
        bind:imgErrorPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
        bind:imgIsCircle="@{true}"
        bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
        bind:imgUrl="@{data.type==3?data.dwLogo:data.photo}" />

      <ImageView
        style="@style/wrap_wrap"
        android:layout_gravity="end|bottom"
        android:visibility="@{data.vip?View.VISIBLE:View.GONE}"
        bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
        bind:imgUrl="@{data.memberLevelIcon}" />

    </FrameLayout>

    <TextView
      android:id="@+id/tv_nick_name"
      style="@style/Text.17sp.000000"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_16"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.type==3?data.dwName:data.nickName}"
      app:layout_constraintBottom_toTopOf="@id/tv_notice_count"
      app:layout_constraintEnd_toStartOf="@id/tv_follow"
      app:layout_constraintStart_toEndOf="@id/fl_avatar"
      app:layout_constraintTop_toTopOf="@id/fl_avatar" />

    <TextView
      android:id="@+id/tv_level"
      style="@style/Text.16sp"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_16"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.type==5?@string/follow_type_personal:@string/follow_type_enterprise}"
      android:textColor="@color/common_4d8fcc"
      app:layout_constraintBottom_toBottomOf="@id/tv_nick_name"
      app:layout_constraintEnd_toStartOf="@id/tv_follow"
      app:layout_constraintStart_toEndOf="@id/tv_nick_name"
      app:layout_constraintTop_toTopOf="@id/tv_nick_name" />

    <TextView
      android:id="@+id/tv_notice_count"
      style="@style/Text.14sp.888888"
      android:layout_width="@dimen/dp_0"
      android:layout_marginEnd="@dimen/dp_12"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.userDesc}"
      app:layout_constraintBottom_toBottomOf="@id/fl_avatar"
      app:layout_constraintEnd_toStartOf="@id/tv_follow"
      app:layout_constraintStart_toStartOf="@id/tv_nick_name"
      app:layout_constraintTop_toBottomOf="@id/tv_nick_name" />

    <TextView
      android:id="@+id/tv_follow"
      style="@style/Text.12sp"
      android:layout_width="55dp"
      android:background="@{data.followed?@drawable/frame_eaeaea_round:@drawable/bg_10c198_round}"
      android:gravity="center"
      android:paddingTop="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_4"
      android:text="@{!data.followed?@string/shuangxuan_follow:@string/shuangxuan_followed}"
      android:textColor="@{!data.followed?@color/common_white:@color/cl_999999}"
      android:visibility="@{data.isSelf()?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toBottomOf="@id/fl_avatar"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/fl_avatar" />

    <View
      style="@style/Line.Horizontal"
      android:layout_marginTop="@dimen/dp_16"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/fl_avatar" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>