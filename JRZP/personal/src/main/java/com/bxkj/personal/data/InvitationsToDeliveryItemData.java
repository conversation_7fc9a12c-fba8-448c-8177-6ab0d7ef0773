package com.bxkj.personal.data;

import com.bxkj.common.util.CheckUtils;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 投递邀请item
 * @TODO: TODO
 * @date 2018/9/26
 */
public class InvitationsToDeliveryItemData {

  private int id;
  private int resid;
  private int relid;
  private String applyTime;
  private int state;
  private String relName;
  private String moneyName;
  private String wtName;
  private String quaName;
  private String shiName;
  private String quName;
  private int cid;
  private String comName;
  private int type;

  private String jnName;

  private String moneyUnitName;
  private String moneyJiesuanName;

  @SerializedName("shenfenName")
  private String identityRequire;

  @SerializedName("jnName2")
  private String partnerNature;

  public String getJnName() {
    return jnName;
  }

  public void setJnName(String jnName) {
    this.jnName = jnName;
  }

  public boolean emptyNatureName() {
    return CheckUtils.isNullOrEmpty(jnName);
  }

  public String getMoneyUnitName() {
    return moneyUnitName;
  }

  public void setMoneyUnitName(String moneyUnitName) {
    this.moneyUnitName = moneyUnitName;
  }

  public String getMoneyJiesuanName() {
    return moneyJiesuanName;
  }

  public void setMoneyJiesuanName(String moneyJiesuanName) {
    this.moneyJiesuanName = moneyJiesuanName;
  }

  public String getIdentityRequire() {
    return identityRequire;
  }

  public void setIdentityRequire(String identityRequire) {
    this.identityRequire = identityRequire;
  }

  public String getPartnerNature() {
    return partnerNature;
  }

  public void setPartnerNature(String partnerNature) {
    this.partnerNature = partnerNature;
  }

  public int getCid() {
    return cid;
  }

  public void setCid(int cid) {
    this.cid = cid;
  }

  public String getComName() {
    return comName;
  }

  public void setComName(String comName) {
    this.comName = comName;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getResid() {
    return resid;
  }

  public void setResid(int resid) {
    this.resid = resid;
  }

  public int getRelid() {
    return relid;
  }

  public void setRelid(int relid) {
    this.relid = relid;
  }

  public String getApplyTime() {
    return applyTime;
  }

  public void setApplyTime(String applyTime) {
    this.applyTime = applyTime;
  }

  public String getFormatApplyTime() {
    return CheckUtils.isNullOrEmpty(applyTime) ? "未知" : applyTime.split("\\s")[0];
  }

  public int getState() {
    return state;
  }

  public void setState(int state) {
    this.state = state;
  }

  public String getRelName() {
    return relName;
  }

  public void setRelName(String relName) {
    this.relName = relName;
  }

  public String getMoneyName() {
    return moneyName;
  }

  public void setMoneyName(String moneyName) {
    this.moneyName = moneyName;
  }

  public String getWtName() {
    return wtName;
  }

  public String getFormatExpName() {
    return CheckUtils.isNullOrEmpty(wtName) ? "经验不限" : wtName;
  }

  public void setWtName(String wtName) {
    this.wtName = wtName;
  }

  public String getQuaName() {
    return quaName;
  }

  public String getFormatEduName() {
    return CheckUtils.isNullOrEmpty(quaName) ? "学历不限" : quaName;
  }

  public void setQuaName(String quaName) {
    this.quaName = quaName;
  }

  public String getShiName() {
    return shiName;
  }

  public void setShiName(String shiName) {
    this.shiName = shiName;
  }

  public String getQuName() {
    return quName;
  }

  public void setQuName(String quName) {
    this.quName = quName;
  }

  public String getTypeText() {
    if (type == 0) {
      return "社招";
    } else {
      return "校招";
    }
  }

  public Boolean isNormalRecruitment() {
    return type == 0;
  }

  public String getConvertSalary() {
    if (!CheckUtils.isNullOrEmpty(getMoneyName()) && getMoneyName().equals("面议")) {
      return getMoneyName();
    } else {
      final StringBuilder salaryBuilder = new StringBuilder();
      salaryBuilder.append(getMoneyName());
      if (!CheckUtils.isNullOrEmpty(moneyUnitName)) {
        salaryBuilder.append("/").append(moneyUnitName);
      }
      if (!CheckUtils.isNullOrEmpty(moneyJiesuanName)) {
        salaryBuilder.append("/").append(moneyJiesuanName);
      }
      return salaryBuilder.toString();
    }
  }
}
