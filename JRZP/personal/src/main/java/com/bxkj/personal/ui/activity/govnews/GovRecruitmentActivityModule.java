package com.bxkj.personal.ui.activity.govnews;

import com.bxkj.common.di.scope.PerFragment;
import com.bxkj.personal.ui.fragment.home.GovRecruitmentFragment;
import com.bxkj.personal.ui.fragment.homenews.RecruitmentNewsFragment;
import com.bxkj.personal.ui.fragment.homesubjob.HomeSubJobFragment;
import com.bxkj.personal.ui.fragment.qa.QAFragment;
import com.bxkj.personal.ui.fragment.study.StudyFragment;
import dagger.Module;
import dagger.android.ContributesAndroidInjector;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal
 * @Description:
 * @TODO: TODO
 * @date 2018/3/30
 */

@Module
public abstract class GovRecruitmentActivityModule {

  @PerFragment
  @ContributesAndroidInjector
  abstract GovRecruitmentFragment newHomeFragment();

  @PerFragment
  @ContributesAndroidInjector
  abstract RecruitmentNewsFragment homeNewsFragment();

  @PerFragment
  @ContributesAndroidInjector
  abstract HomeSubJobFragment homeSubJobFragment();

  @PerFragment
  @ContributesAndroidInjector
  abstract QAFragment qaFragment();

  @PerFragment
  @ContributesAndroidInjector
  abstract StudyFragment learnFragment();
}
