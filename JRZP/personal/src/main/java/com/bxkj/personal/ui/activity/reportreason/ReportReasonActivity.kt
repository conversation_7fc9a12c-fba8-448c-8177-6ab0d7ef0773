package com.bxkj.personal.ui.activity.reportreason

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.data.ReportReasonItemData
import com.bxkj.personal.R.layout
import com.bxkj.personal.databinding.PersonalActivityReportReasonBinding

/**
 * @description: 举报原因
 * @author:45457
 * @date: 2020/7/8
 * @version: V1.0
 */
class ReportReasonActivity :
  BaseDBActivity<PersonalActivityReportReasonBinding, ReportReasonViewModel>() {

  companion object {

    const val EXTRA_RESULT_REASON = "RESULT_REASON"

    fun newIntent(context: Context): Intent {
      return Intent(context, ReportReasonActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<ReportReasonViewModel> = ReportReasonViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_report_reason

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupReasonAdapter()
    viewModel.start()
  }

  private fun setupReasonAdapter() {
    val reportReasonListAdapter =
      SimpleDiffListAdapter<ReportReasonItemData>(
        layout.c_recycler_report_reason_item,
        ReportReasonItemData.DiffCallBack()
      ).apply {
        setOnItemClickListener(object :
          SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            getData()?.let {
              backAndResult(it[position])
            }
          }
        })
      }
    viewBinding.recyclerReason.layoutManager = LinearLayoutManager(this)
    viewBinding.recyclerReason.adapter = reportReasonListAdapter
  }

  private fun backAndResult(reportReasonItem: ReportReasonItemData) {
    setResult(Activity.RESULT_OK, Intent().apply {
      putExtra(EXTRA_RESULT_REASON, reportReasonItem.name)
    })
    finish()
  }
}