package com.bxkj.personal.ui.activity.createresumesteptwo

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import com.bigkoo.pickerview.view.OptionsPickerView
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.BaseDaggerActivity
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.constants.RouterConstants
import com.bxkj.common.data.JobTypeData
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.mvp.mvp.BasePresenter
import com.bxkj.common.mvp.mvp.BaseView
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.TitleBarManager
import com.bxkj.common.util.picker.OneOptionPicker
import com.bxkj.common.util.picker.PickerUtils
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.widget.adresspickerdialog.AddressData
import com.bxkj.common.widget.adresspickerdialog.AddressPickerDialogFragment
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.common.widget.popup.menupopup.MenuPopup.Builder
import com.bxkj.jrzp.user.videorelate.ui.MyVideoRelateNavigation
import com.bxkj.personal.R
import com.bxkj.personal.R.array
import com.bxkj.personal.data.CreateResumeData
import com.bxkj.personal.data.ResumeDefaultData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.databinding.PersonalActivityCreateResumeStepTwoBinding
import com.bxkj.personal.ui.activity.bindmobile.BindMobileActivity
import com.bxkj.personal.ui.activity.createresumestepthree.CreateResumeStepThreeActivity
import com.bxkj.personal.ui.activity.main.MainActivity
import com.bxkj.personal.ui.activity.myresumelist.MyResumeListActivity
import com.bxkj.personal.ui.activity.selectresume.SelectResumeActivity
import com.bxkj.personal.ui.activity.uploadattechmentresume.AttachmentResumeNavigation
import com.therouter.router.Route
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2018/11/27
 * @version V1.0
 */
@Route(path = CreateResumeNavigation.PATH)
class CreateResumeStepTwoActivity :
  BaseDaggerActivity(),
  CreateResumeStepTwoContract.View,
  View.OnClickListener {
  @Inject
  lateinit var mCreateResumeStepTwoPresenter: CreateResumeStepTwoPresenter

  @Inject
  lateinit var mAccountRoute: AccountRepo

  private lateinit var mWorkNaturePicker: OptionsPickerView<PickerOptionsData>

  private lateinit var mWorkExpPicker: OptionsPickerView<PickerOptionsData>
  private lateinit var mDutyTimePicker: OptionsPickerView<PickerOptionsData>
  private lateinit var mAddressPickerDialogFragment: AddressPickerDialogFragment

  private var mResumeOpenStatePopup: MenuPopup? = null

  companion object {
    private const val TO_SELECT_EXPECT_POSITION_CODE = 1
    private const val TO_BIND_MOBILE_CODE = 2

    fun newIntent(
      context: Context,
      createOrigin: Int,
    ): Intent {
      val intent = Intent(context, CreateResumeStepTwoActivity::class.java)
      intent.putExtra(RouterConstants.CREATE_RESUME_FROM, createOrigin)
      return intent
    }
  }

  private lateinit var mResumeBasicInfo: CreateResumeData
  private lateinit var mViewBinding: PersonalActivityCreateResumeStepTwoBinding

  override fun initPresenter(presenters: MutableList<BasePresenter<BaseView>>): MutableList<BasePresenter<BaseView>> {
    presenters.add(CheckUtils.cast(mCreateResumeStepTwoPresenter))
    return presenters
  }

  override fun getLayoutId(): Int = 0

  override fun onCreate(savedInstanceState: Bundle?) {
    mViewBinding = PersonalActivityCreateResumeStepTwoBinding.inflate(layoutInflater)
    mViewBinding.lifecycleOwner = this
    setContentView(mViewBinding.root)
    super.onCreate(savedInstanceState)
  }

  override fun initTitleBar(titleBarManager: TitleBarManager?) {
    titleBarManager?.setTitle(getString(R.string.personal_career_objective))
  }

  override fun initPage() {
    mResumeBasicInfo = CreateResumeData()

    initWorkNaturePicker()
    initWorkPlacePicker()
    initWorkExpPicker()
    initDutyTimePicker()

    setupResumeOpenStatePicker()

    setupOnClickListener()
    checkNeedBingMobile()
  }

  private fun setupResumeOpenStatePicker() {
    val openStates = resources.getStringArray(array.common_resume_show_states)
    mViewBinding.tvPrivacyState.text = openStates[0]
    mResumeOpenStatePopup =
      Builder(this)
        .setShowMode(MenuPopup.BOTTOM)
        .setSelected(mViewBinding.tvPrivacyState.text.toString())
        .setData(openStates)
        .setOnItemClickListener { _: View?, position: Int ->
          mResumeBasicInfo.state = (if (position == 0) 0 else 2)
          mViewBinding.tvPrivacyState.text = openStates[position]
        }.build()
  }

  private fun checkNeedBingMobile() {
    showLoading()
    mAccountRoute.checkIsBindMobile(
      mUserID,
      object : ResultCallBack {
        override fun onSuccess() {
          hiddenLoading()
          ActionDialog
            .Builder()
            .setCancelable(false)
            .setTitle(getString(R.string.bind_mobile_title))
            .setContent(getString(R.string.create_resume_need_bind_mobile))
            .setConfirmText(getString(R.string.create_resume_to_bind_mobile))
            .setOnConfirmClickListener {
              startActivityForResult(
                BindMobileActivity.newIntent(this@CreateResumeStepTwoActivity),
                TO_BIND_MOBILE_CODE,
              )
            }.setOnCancelClickListener {
              finish()
            }.build()
            .show(supportFragmentManager)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          mCreateResumeStepTwoPresenter.getCreateResumeDefaultInfo(mUserID)
        }
      },
    )
  }

  private fun setupOnClickListener() {
    mViewBinding.llExpectCategory.setOnClickListener(this)
    mViewBinding.tvWorkingNature.setOnClickListener(this)
    mViewBinding.tvWorkPlace.setOnClickListener(this)
    mViewBinding.tvWorkExp.setOnClickListener(this)
    mViewBinding.tvPrivacyStateText.setOnClickListener(this)
    mViewBinding.tvPrivacyState.setOnClickListener(this)
    mViewBinding.tvDutyTime.setOnClickListener(this)
    mViewBinding.btnConfirm.setOnClickListener(this)
  }

  override fun onClick(v: View) {
    SystemUtil.hideSoftKeyboard(this)
    when (v.id) {
      R.id.ll_expect_category -> {
        Router
          .getInstance()
          .to(RouterNavigation.JobSelectActivity.URL)
          .withInt(
            RouterNavigation.TO_ACTIVITY_TYPE,
            RouterNavigation.JobSelectActivity.RESULT,
          ).withInt(RouterNavigation.JobSelectActivity.MAX_SELECT_COUNT, 1)
          .startForResult(this, TO_SELECT_EXPECT_POSITION_CODE)
      }

      R.id.tv_working_nature -> {
        mWorkNaturePicker.show()
      }

      R.id.tv_work_place -> {
        mAddressPickerDialogFragment.setSelected(
          mResumeBasicInfo.shengName,
          mResumeBasicInfo.shiName,
          mResumeBasicInfo.zhenName,
          mResumeBasicInfo.xiangName,
        )
        mAddressPickerDialogFragment.show(
          supportFragmentManager,
          AddressPickerDialogFragment.TAG,
        )
      }

      R.id.tv_work_exp -> {
        mWorkExpPicker.show()
      }

      R.id.tv_privacy_state_text -> {
        TipsDialog()
          .setTitle(getString(R.string.my_resume_privacy_state_desc_title))
          .setContent(getString(R.string.my_resume_privacy_state_desc_content))
          .setContentGravity(Gravity.START)
          .show(supportFragmentManager)
      }

      R.id.tv_privacy_state -> {
        mResumeOpenStatePopup?.show()
      }

      R.id.tv_duty_time -> {
        mDutyTimePicker.show()
      }

      R.id.btn_confirm -> {
        submitData()
      }
    }
  }

  /**
   * 初始化工作性質picker
   */
  private fun initWorkNaturePicker() {
    val pickerOptionsData =
      PickerUtils.parsePickerDataList(*resources.getStringArray(R.array.personal_work_nature))
    mWorkNaturePicker =
      OneOptionPicker(this)
        .setOptionsDataList(pickerOptionsData)
        .setOnConfirmListener { position ->
          mViewBinding.tvWorkingNature.text = pickerOptionsData[position].name
          mResumeBasicInfo.jnid = position + 7
        }.create()
  }

  /**
   * 初始化工作地点picker
   */
  private fun initWorkPlacePicker() {
    mAddressPickerDialogFragment = AddressPickerDialogFragment()
    mAddressPickerDialogFragment.setOnSelectedListener { province, city, area, street ->
      mViewBinding.tvWorkPlace.text =
        String.format(
          "%s%s%s",
          if (province.name == city.name) province.name else province.name + city.name,
          area.name,
          street.name,
        )
      mResumeBasicInfo.setAddress(AddressData(province, city, area, street))
    }
  }

  /**
   * 初始化工作经验picker
   */
  private fun initWorkExpPicker() {
    val pickerOptionsData =
      PickerUtils.parsePickerDataList(*resources.getStringArray(R.array.personal_work_exp))
    mWorkExpPicker =
      OneOptionPicker(this)
        .setOptionsDataList(pickerOptionsData)
        .setOnConfirmListener { position ->
          mViewBinding.tvWorkExp.text = pickerOptionsData[position].name
          mResumeBasicInfo.wtid = position + 2
        }.create()
  }

  /**
   * 初始化到岗时间picker
   */
  private fun initDutyTimePicker() {
    val pickerOptionsData =
      PickerUtils.parsePickerDataList(*resources.getStringArray(R.array.personal_duty_time))
    mDutyTimePicker =
      OneOptionPicker(this)
        .setOptionsDataList(pickerOptionsData)
        .setOnConfirmListener { position ->
          mViewBinding.tvDutyTime.text = pickerOptionsData[position].name
          mResumeBasicInfo.daogang = position + 1
        }.create()
  }

  private fun submitData() {
    mResumeBasicInfo.uid = mUserID
    mResumeBasicInfo.willMoney = mViewBinding.etExpectedSalary.text.toString()
    mResumeBasicInfo.detailsName = mViewBinding.etBeforeJob.text.toString()
    mResumeBasicInfo.detailsName2 = mViewBinding.etSpecificPosition.text.toString()
    mCreateResumeStepTwoPresenter.createResume(mResumeBasicInfo)
  }

  override fun getDefaultInfoSuccess(resumeDefaultData: ResumeDefaultData?) {
    resumeDefaultData?.let {
      mResumeBasicInfo.sheng = resumeDefaultData.province
      mResumeBasicInfo.shengName = resumeDefaultData.provinceName
      mResumeBasicInfo.shi = resumeDefaultData.city
      mResumeBasicInfo.shiName = resumeDefaultData.cityName
      mViewBinding.tvWorkPlace.text = mResumeBasicInfo.address
      mResumeBasicInfo.wtid = resumeDefaultData.wtId
      mResumeBasicInfo.wtName = resumeDefaultData.wtName
      mViewBinding.tvWorkExp.text = resumeDefaultData.wtName
      mResumeBasicInfo.jnid = resumeDefaultData.jnId
      mResumeBasicInfo.jnName = resumeDefaultData.jnName
      mViewBinding.tvWorkingNature.text = resumeDefaultData.jnName
    }
  }

  override fun createResumeSuccess(resumeId: Int) {
    showNextStepSelectPopup(resumeId)
  }

  private fun showNextStepSelectPopup(resumeId: Int) {
    MenuPopup
      .Builder(this)
      .setShowMode(MenuPopup.BOTTOM)
      .setData(resources.getStringArray(R.array.create_resume_three_step_options))
      .setOnItemClickListener { _, position ->
        var nextIntent: Intent? = null
        if (position == 0) {
          nextIntent =
            AttachmentResumeNavigation.create().createIntent(this)
        } else {
          val intent = Intent(this, CreateResumeStepThreeActivity::class.java)
          intent.putExtras(getIntent())
          intent.putExtra(RouterConstants.RESUME_ID, resumeId)
          startActivity(intent)
        }
        nextIntent?.let {
          it.putExtras(intent)
          startActivity(it)
        }
        finish()
      }.setCancelText(getString(R.string.jump_over))
      .setOnCancelClickListener {
        when (
          intent.getIntExtra(
            RouterConstants.CREATE_RESUME_FROM,
            RouterConstants.CREATE_RESUME_FROM_REGISTER_OR_LOGIN,
          )
        ) {
          // 来源为登录或注册
          RouterConstants.CREATE_RESUME_FROM_REGISTER_OR_LOGIN ->
            startActivity(
              Intent(
                this,
                MainActivity::class.java,
              ),
            )
          // 来源为投递简历
          RouterConstants.CREATE_RESUME_FROM_APPLICATION -> {
            val intent = Intent(this, SelectResumeActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
            startActivity(intent)
          }
          // 来源为简历列表
          RouterConstants.CREATE_RESUME_FROM_RESUME_LIST -> {
            val intent = Intent(this, MyResumeListActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
            startActivity(intent)
          }
          // 来源为视频关联
          RouterConstants.CREATE_RESUME_FROM_VIDEO_RELATE -> {
            MyVideoRelateNavigation
              .navigate()
              .withFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
              .start(this)
          }
        }
        finish()
      }.build()
      .show()
  }

  override fun onActivityResult(
    requestCode: Int,
    resultCode: Int,
    data: Intent?,
  ) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_SELECT_EXPECT_POSITION_CODE) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val resultFirstId =
          data.getIntExtra(
            RouterNavigation.JobSelectActivity.RESULT_FIRST_CLASS_ID,
            CommonApiConstants.NO_DATA,
          )
        val resultFirstName =
          data.getStringExtra(RouterNavigation.JobSelectActivity.RESULT_FIRST_CLASS_NAME)
        val jobClassData =
          data.getParcelableArrayListExtra<JobTypeData>(RouterNavigation.JobSelectActivity.RESULT_DATA)
        jobClassData?.let {
          mViewBinding.tvExpectPosition.text =
            getString(
              R.string.personal_between_by_diagonal,
              resultFirstName,
              jobClassData[0].name,
            )
          mResumeBasicInfo.jobType1 = resultFirstId
          mResumeBasicInfo.jobType2 = jobClassData[0].id
        }
      }
    } else if (requestCode == TO_BIND_MOBILE_CODE) {
      if (resultCode != RouterNavigation.BindMobileActivity.RESULT_BIND_SUCCESS) {
        finish()
      }
    }
  }
}
