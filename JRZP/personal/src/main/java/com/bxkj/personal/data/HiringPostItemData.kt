package com.bxkj.personal.data

import androidx.recyclerview.widget.DiffUtil

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data.db
 * @Description:
 * <AUTHOR>
 * @date 2019/10/16
 * @version V1.0
 */
data class HiringPostItemData(var id:Int
                              ,var comLogo:String
                              ,var cid:Int
                              ,var title:String
                              ,var ksdate:String
                              ,var jsdate:String
                              ,var createTime:String)

class HiringPostItemCallBack : DiffUtil.ItemCallback<HiringPostItemData>() {
    override fun areItemsTheSame(oldItem: HiringPostItemData, newItem: HiringPostItemData): Boolean {
        return oldItem == newItem
    }

    override fun areContentsTheSame(oldItem: HiringPostItemData, newItem: HiringPostItemData): Boolean {
        return oldItem.id == newItem.id
    }
}