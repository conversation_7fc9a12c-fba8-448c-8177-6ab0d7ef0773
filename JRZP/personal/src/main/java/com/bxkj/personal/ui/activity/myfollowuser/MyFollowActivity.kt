package com.bxkj.personal.ui.activity.myfollowuser

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R.id
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.FollowItemData
import com.bxkj.personal.databinding.PersonalActivityMyFollowBinding
import com.bxkj.personal.ui.activity.gzuserhome.GzUserHomeActivity

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.myfollow
 * @Description: 我关注的
 * <AUTHOR>
 * @date 2019/12/9
 * @version V1.0
 */
@Route(path = MyFollowNavigation.PATH)
class MyFollowActivity : BaseDBActivity<PersonalActivityMyFollowBinding, MyFollowViewModel>() {

  companion object {

    const val TO_USER_HOME_CODE = 1
    const val TO_NEW_USER_HOME_CODE = 2

    fun newIntent(context: Context, queryUserId: Int, isEnterprise: Boolean): Intent {
      val intent = Intent(context, MyFollowActivity::class.java)
      intent.putExtra(MyFollowNavigation.EXTRA_QUERY_USER_ID, queryUserId)
      intent.putExtra(MyFollowNavigation.EXTRA_IS_ENTERPRISE, isEnterprise)
      return intent
    }
  }

  private var mFollowUserListAdapter: SimpleDBListAdapter<FollowItemData>? = null

  override fun getViewModelClass(): Class<MyFollowViewModel> = MyFollowViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_my_follow

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    setupFollowUserListAdapter()
    viewModel.start(intent)
  }

  private fun setupFollowUserListAdapter() {
    mFollowUserListAdapter =
      SimpleDBListAdapter<FollowItemData>(
        this,
        layout.personal_recycler_follow_list_item
      ).apply {
        setOnItemClickListener(object :
          SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            val userItem = data[position]
            if (v.id == id.tv_follow) {
              if (userItem.isFollowed) {
                showCancelFollowConfirmDialog(position, data[position])
              } else {
                viewModel.addOrRemoveFollow(position, userItem)
              }
            } else {
              if (userItem.isEnterprise) {
                if (AuthenticationType.higherEnterpriseAuth(userItem.rzType)) {
                  UserHomeNavigation.navigate(userItem.id)
                    .startForResult(this@MyFollowActivity, TO_NEW_USER_HOME_CODE)
                } else {
                  startActivityForResult(
                    GzUserHomeActivity.newIntent(
                      this@MyFollowActivity,
                      if (userItem.isEnterprise) CommonApiConstants.NO_ID else userItem.id,
                      queryEnterpriseId = if (userItem.isEnterprise) userItem.id else CommonApiConstants.NO_ID,
                      affectPosition = position
                    ), TO_USER_HOME_CODE
                  )
                }
              } else {
                UserHomeNavigation.navigate(userItem.id)
                  .startForResult(this@MyFollowActivity, TO_NEW_USER_HOME_CODE)
              }
            }
          }
        }, id.tv_follow)
      }
    val recyclerFollowUserList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerFollowUserList.layoutManager = LinearLayoutManager(this)

    viewModel.listViewModel.setAdapter(mFollowUserListAdapter)
  }

  private fun showCancelFollowConfirmDialog(position: Int, userItem: FollowItemData) {
    ActionDialog.Builder()
      .setTitle(getString(R.string.followed_cancel_follow_confirm_dialog_title))
      .setContent(getString(R.string.followed_cancel_follow_confirm_dialog_content))
      .setOnConfirmClickListener {
        viewModel.addOrRemoveFollow(position, userItem)
      }.build().show(supportFragmentManager)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

}