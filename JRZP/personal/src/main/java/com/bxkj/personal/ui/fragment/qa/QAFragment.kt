package com.bxkj.personal.ui.fragment.qa

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.databinding.IncludeMvvmRefreshLayoutBinding
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.NewsItemData.PhotoListBean
import com.bxkj.personal.data.QAUserData
import com.bxkj.personal.data.QuestionItemData
import com.bxkj.personal.data.QuestionItemData.UnansweredQuestionGroup
import com.bxkj.personal.ui.activity.addquestion.AddQuestionActivity
import com.bxkj.personal.ui.activity.qaranklist.QARankListActivity
import com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsActivity
import com.bxkj.personal.ui.activity.questioninvite.QuestionInviteActivity

/**
 * @Project: gzgk
 * @Description: 问答
 * @author:45457
 * @date: 2020/4/1
 * @version: V1.0
 */
class QAFragment : BaseDBFragment<IncludeMvvmRefreshLayoutBinding, QAViewModel>() {

  companion object {

    const val TO_QUESTION_DETAILS_CODE = 1

    fun newInstance(): Fragment {
      return QAFragment()
    }
  }

  override fun getViewModelClass(): Class<QAViewModel> = QAViewModel::class.java

  override fun getLayoutId(): Int = R.layout.include_mvvm_refresh_layout

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.listViewModel = viewModel.qaListViewModel

    subscribeLoginSuccess()
    setupQAListAdapter()

  }

  private fun subscribeLoginSuccess() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_LOGIN_SUCCESS) {
            viewModel.refresh()
          }
        }
    )
  }

  override fun lazyLoadData() {
    super.lazyLoadData()
    viewModel.refresh()
  }

  private fun setupQAListAdapter() {
    val qaListAdapter = MultiTypeAdapter(parentActivity)
    qaListAdapter.register(QuestionItemData.UnLogin::class.java
      , DefaultViewBinder<QuestionItemData.UnLogin>(
            R.layout.personal_recycler_qa_unlogin_layout,
            DefaultViewBinder.NO_BR_ID,
            true
      ).apply {
        setOnItemClickListener(object :
          DefaultViewBinder.OnItemClickListener<QuestionItemData.UnLogin> {
          override fun onItemClicked(v: View, position: Int, item: QuestionItemData.UnLogin) {
            when (v.id) {
              R.id.tv_to_answer -> {
                startActivity(
                  QuestionInviteActivity.newIntent(
                    parentActivity,
                    toRecommendPage = true
                  )
                )
              }
              R.id.tv_rank_list -> {
                startActivity(QARankListActivity.newIntent(parentActivity))
              }
              else -> {
                checkLoginAndToLogin()
              }
            }
          }
        }, R.id.tv_to_answer, R.id.tv_rank_list)
      })
    qaListAdapter.register(QAUserData::class.java
      , DefaultViewBinder<QAUserData>(
            R.layout.personal_recycler_qa_login_layout,
            BR.data,
            true
      )
        .apply {
          setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<QAUserData> {
            override fun onItemClicked(v: View, position: Int, item: QAUserData) {
              when (v.id) {
                R.id.cl_user_info -> {
                  UserHomeNavigation.navigate(localUserId).start()
                }
                R.id.tv_to_answer -> {
                  startActivity(
                    QuestionInviteActivity.newIntent(
                      parentActivity,
                      toRecommendPage = true
                    )
                  )
                }
                R.id.tv_add_question -> {
                  startActivity(AddQuestionActivity.newIntent(parentActivity))
                }
                R.id.tv_rank_list -> {
                  startActivity(QARankListActivity.newIntent(parentActivity))
                }
              }
            }
          }, R.id.cl_user_info, R.id.tv_to_answer, R.id.tv_add_question, R.id.tv_rank_list)
        })
    //未回答问题
    qaListAdapter.register(QuestionItemData.UnansweredQuestionGroup::class.java
      , object : DefaultViewBinder<QuestionItemData.UnansweredQuestionGroup>(
            R.layout.personal_recycler_unanswered_question_item,
            BR.data,
            true
      ) {
        override fun onBindViewHolder(
          holder: SuperViewHolder,
          item: UnansweredQuestionGroup,
          position: Int
        ) {
          super.onBindViewHolder(holder, item, position)
          val recyclerUnansweredQuestionList =
            holder.findViewById<RecyclerView>(R.id.recycler_unanswered_question)
          recyclerUnansweredQuestionList.layoutManager =
            LinearLayoutManager(holder.itemView.context)
          recyclerUnansweredQuestionList.adapter = object : SimpleDBListAdapter<QuestionItemData>(
            holder.itemView.context,
            R.layout.personal_recycler_home_recommend_question
          ) {
            override fun convert(
              holder: SuperViewHolder,
              viewType: Int,
              item: QuestionItemData,
              position: Int
            ) {
              super.convert(holder, viewType, item, position)
              if (!CheckUtils.isNullOrEmpty(item.photoList)) {
                val showList =
                  if (item.photoList.size > 3) item.photoList.subList(0, 3) else item.photoList
                val recyclerAnswererList =
                  holder.findViewById<RecyclerView>(R.id.recycler_answerer_list)
                recyclerAnswererList.isNestedScrollingEnabled = false
                recyclerAnswererList.layoutManager = LinearLayoutManager(
                  holder.itemView.context,
                  LinearLayoutManager.HORIZONTAL,
                  false
                )
                val paidUserListAdapter =
                  SimpleDBListAdapter<PhotoListBean>(
                    holder.itemView.context,
                    layout.personal_recycler_answerer_avatar_item
                  )
                recyclerAnswererList.adapter = paidUserListAdapter
                paidUserListAdapter.reset(showList)
              }
            }
          }.apply {
            reset(item.data)
            setOnItemClickListener(object :
              com.bxkj.common.adapter.superadapter.SuperItemClickListener {
              override fun onClick(v: View, position: Int) {
                startActivityForResult(
                  QuestionDetailsActivity.newIntent(
                    parentActivity,
                    data[position].id
                  ), TO_QUESTION_DETAILS_CODE
                )
              }
            })
          }
        }
      })
    qaListAdapter.register(
      QuestionItemData::class.java,
      object : DefaultViewBinder<QuestionItemData>(
          R.layout.personal_recycler_home_recommend_question,
          BR.data,
          true
      ) {
        var lastPosition: Int = 0
        override fun onBindViewHolder(
          holder: SuperViewHolder,
          item: QuestionItemData,
          position: Int
        ) {
          super.onBindViewHolder(holder, item, position)
          if (!CheckUtils.isNullOrEmpty(item.photoList)) {
            val showList =
              if (item.photoList.size > 3) item.photoList.subList(0, 3) else item.photoList
            val recyclerAnswererList =
              holder.findViewById<RecyclerView>(R.id.recycler_answerer_list)
            recyclerAnswererList.isNestedScrollingEnabled = false
            recyclerAnswererList.layoutManager =
              LinearLayoutManager(holder.itemView.context, LinearLayoutManager.HORIZONTAL, false)
            val paidUserListAdapter =
              SimpleDBListAdapter<PhotoListBean>(
                holder.itemView.context,
                layout.personal_recycler_answerer_avatar_item
              )
            recyclerAnswererList.adapter = paidUserListAdapter
            paidUserListAdapter.reset(showList)
          }
          if (position % 15 == 10) {
            if (lastPosition < position)
              viewModel.loadMore()
          }
          lastPosition = position
        }
      }.apply {
        setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<QuestionItemData> {
          override fun onItemClicked(v: View, position: Int, item: QuestionItemData) {
            startActivity(QuestionDetailsActivity.newIntent(parentActivity, item.id))
          }
        })
      })
    viewBinding.recyclerContent.layoutManager = LinearLayoutManager(parentActivity)
    viewModel.qaListViewModel.setAdapter(qaListAdapter)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_QUESTION_DETAILS_CODE) {
      viewModel.refreshUnansweredQuestion()
    }
  }
}