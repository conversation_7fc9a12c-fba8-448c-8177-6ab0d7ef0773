package com.bxkj.personal.ui.fragment.maintab

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.enterprise.ui.fragment.homev2.EnterpriseHomeFragmentNavigation
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType
import com.bxkj.personal.R
import com.bxkj.personal.databinding.FragmentMainTabContainerBinding
import com.bxkj.personal.ui.fragment.homev3.GeekHomeFragmentV3

/**
 *
 * @author: sanjin
 * @date: 2022/7/29
 */
class MainTabOneContainerFragment :
    BaseDBFragment<FragmentMainTabContainerBinding, BaseViewModel>() {

    private var currentUserType: Int = 0

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.fragment_main_tab_container

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        refreshShowPage()
        subscribeIdentityChangeEvent()
    }

    private fun subscribeIdentityChangeEvent() {
        addDisposable(
            RxBus.get().toObservable(RxBus.Message::class.java)
                .subscribe {
                    if (it.code == RxMsgCode.ACTION_USER_ROLE_CHANGE) {
                        refreshShowPage()
                    }
                }
        )
    }

    private fun refreshShowPage() {
        if (currentUserType == UserUtils.getUserRole()) {
            return
        }
        if (AuthenticationType.higherEnterpriseAuth(UserUtils.getUserRole())) {
            setupContentFragment(EnterpriseHomeFragmentNavigation.create())
        } else {
            setupContentFragment(GeekHomeFragmentV3.newInstance())
        }
        currentUserType = UserUtils.getUserRole()
    }

    private fun setupContentFragment(fragment: Fragment) {
        childFragmentManager.beginTransaction()
            .replace(R.id.fl_container, fragment)
            .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_FADE)
            .commitAllowingStateLoss()
    }
}