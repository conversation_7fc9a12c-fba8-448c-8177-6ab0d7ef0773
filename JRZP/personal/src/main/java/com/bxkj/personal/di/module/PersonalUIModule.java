package com.bxkj.personal.di.module;

import com.bxkj.common.di.scope.PerActivity;
import com.bxkj.common.di.scope.PerFragment;
import com.bxkj.jrzp.support.comment.ui.replay.CommentReplyActivity;
import com.bxkj.personal.ui.activity.aboutus.AboutUsV2Activity;
import com.bxkj.personal.ui.activity.addanswer.AddAnswerActivity;
import com.bxkj.personal.ui.activity.addquestion.AddQuestionActivity;
import com.bxkj.personal.ui.activity.addshieldcompany.AddShieldCompanyActivity;
import com.bxkj.personal.ui.activity.answerdetails.AnswerDetailsActivity;
import com.bxkj.personal.ui.activity.applicationrecord.ApplicationRecordActivity;
import com.bxkj.personal.ui.activity.applicationrecord.ApplicationRecordModule;
import com.bxkj.personal.ui.activity.applicationrecord.ResumeDeliveryRecordActivity;
import com.bxkj.personal.ui.activity.arealist.AreaListActivity;
import com.bxkj.personal.ui.activity.bindmobile.BindMobileActivity;
import com.bxkj.personal.ui.activity.campusrecruitdetails.CampusRecruitDetailsActivity;
import com.bxkj.personal.ui.activity.campusrecruitdetails.HotCampusRecruitListFragment;
import com.bxkj.personal.ui.activity.campusrecruitdetails.RecruitBrochureFragment;
import com.bxkj.personal.ui.activity.campustalkdetails.CampusTalkDetailsActivity;
import com.bxkj.personal.ui.activity.companydetails.CompanyDetailsActivity;
import com.bxkj.personal.ui.activity.companydetails.CompanyDetailsActivityModule;
import com.bxkj.personal.ui.activity.conversation.GeekChatContentActivity;
import com.bxkj.personal.ui.activity.conversationreport.CommonReportActivity;
import com.bxkj.personal.ui.activity.createresumestepfour.CreateResumeStepFourActivity;
import com.bxkj.personal.ui.activity.createresumestepthree.CreateResumeStepThreeActivity;
import com.bxkj.personal.ui.activity.createresumesteptwo.CreateResumeStepTwoActivity;
import com.bxkj.personal.ui.activity.editrichtext.EditRichTextActivity;
import com.bxkj.personal.ui.activity.fans.FansActivity;
import com.bxkj.personal.ui.activity.feedbackmsgcontent.FeedbackMsgContentActivity;
import com.bxkj.personal.ui.activity.feedbackmsgdetails.FeedbackMsgDetailsActivity;
import com.bxkj.personal.ui.activity.findjobbymap.FindJobOnMapActivity;
import com.bxkj.personal.ui.activity.finearticle.FineArticleActivity;
import com.bxkj.personal.ui.activity.finearticle.FineArticleDetailsActivity;
import com.bxkj.personal.ui.activity.gallery.GalleryActivity;
import com.bxkj.personal.ui.activity.govnews.GovRecruitmentActivity;
import com.bxkj.personal.ui.activity.govnews.GovRecruitmentActivityModule;
import com.bxkj.personal.ui.activity.gznews.GzNewsDetailsActivity;
import com.bxkj.personal.ui.activity.gzuserhome.GzUserHomeActivity;
import com.bxkj.personal.ui.activity.gzuserhome.GzUserHomeModule;
import com.bxkj.personal.ui.activity.hotdiscuss.HotDiscussActivity;
import com.bxkj.personal.ui.activity.hotdiscuss.HotDiscussRankActivity;
import com.bxkj.personal.ui.activity.integralrecharge.IntegralRechargeActivity;
import com.bxkj.personal.ui.activity.interviewdetails.GeekInterviewDetailsActivity;
import com.bxkj.personal.ui.activity.invitationstodelivery.InvitationsToDeliveryActivity;
import com.bxkj.personal.ui.activity.invitationstodelivery.InvitationsToDeliveryModule;
import com.bxkj.personal.ui.activity.inviteuser.InviteUserActivity;
import com.bxkj.personal.ui.activity.inviteuser.InviteUserModule;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivity;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2;
import com.bxkj.personal.ui.activity.jobintention.JobIntentionActivity;
import com.bxkj.personal.ui.activity.lastjob.LatestJobActivity;
import com.bxkj.personal.ui.activity.main.MainActivity;
import com.bxkj.personal.ui.activity.main.PersonalMainActivityModule;
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoActivity;
import com.bxkj.personal.ui.activity.minesearchjobs.MineSearchJobsActivity;
import com.bxkj.personal.ui.activity.momentdetails.MomentDetailsActivity;
import com.bxkj.personal.ui.activity.msgnotification.MsgNotificationActivity;
import com.bxkj.personal.ui.activity.msgnotification.NewMsgNotificationActivity;
import com.bxkj.personal.ui.activity.msgnotificationcontent.MsgNotificationContentActivity;
import com.bxkj.personal.ui.activity.msgnotificationcontent.NewMsgNotificationContentActivity;
import com.bxkj.personal.ui.activity.mycollectioncompany.MyCollectionCompanyActivity;
import com.bxkj.personal.ui.activity.mycollectionjobs.MyCollectionJobsActivity;
import com.bxkj.personal.ui.activity.myfollowuser.MyFollowActivity;
import com.bxkj.personal.ui.activity.myhistory.MyActivityModule;
import com.bxkj.personal.ui.activity.myhistory.MyHistoryActivity;
import com.bxkj.personal.ui.activity.myresume.MyResumeDetailsActivityV2;
import com.bxkj.personal.ui.activity.myresume.SelfEvaluationActivity;
import com.bxkj.personal.ui.activity.myresume.careerobjective.CareerObjectiveActivity;
import com.bxkj.personal.ui.activity.myresume.certificate.CertificateActivity;
import com.bxkj.personal.ui.activity.myresume.edubackground.EduBackgroundActivity;
import com.bxkj.personal.ui.activity.myresume.languageskills.LanguageSkillsActivity;
import com.bxkj.personal.ui.activity.myresume.professionalskill.ProfessionalSkillActivity;
import com.bxkj.personal.ui.activity.myresume.schoolsituation.SchoolSituationActivity;
import com.bxkj.personal.ui.activity.myresume.workexp.EditWorkExpActivity;
import com.bxkj.personal.ui.activity.myresume.workexp.WorkExpActivity;
import com.bxkj.personal.ui.activity.myresumelist.MyResumeListActivity;
import com.bxkj.personal.ui.activity.onlinecampustalkdetails.OnlineCampusTalkDetailsActivity;
import com.bxkj.personal.ui.activity.orderhistory.OrderHistoryActivity;
import com.bxkj.personal.ui.activity.orderhistory.OrderHistoryModule;
import com.bxkj.personal.ui.activity.paiduser.PaidUserActivity;
import com.bxkj.personal.ui.activity.parttimejob.PartTimeJobContainerActivity;
import com.bxkj.personal.ui.activity.parttimejob.PartTimeJobFragment;
import com.bxkj.personal.ui.activity.parttimejobtypelist.PartTimeJobSearchResultActivity;
import com.bxkj.personal.ui.activity.parttimejobtypelist.PartTimeJobTypeListActivity;
import com.bxkj.personal.ui.activity.parttimeworkbench.PartTimeWorkbenchActivity;
import com.bxkj.personal.ui.activity.parttimeworkbench.customerlist.CustomerListFragment;
import com.bxkj.personal.ui.activity.parttimeworkbench.customersearchresult.CustomerSearchResultActivity;
import com.bxkj.personal.ui.activity.parttimeworkbench.incomeanalyzer.IncomeAnalyzerDialogFragment;
import com.bxkj.personal.ui.activity.parttimeworkbench.myprofit.MyProfitFragment;
import com.bxkj.personal.ui.activity.parttimeworkbench.workbench.WorkBenchFragment;
import com.bxkj.personal.ui.activity.paymentorder.PaymentOrderActivity;
import com.bxkj.personal.ui.activity.paymentresult.PaymentResultActivity;
import com.bxkj.personal.ui.activity.permissionmanagement.PermissionManagementActivity;
import com.bxkj.personal.ui.activity.personalbasicinformation.PersonalBasicInfoActivity;
import com.bxkj.personal.ui.activity.personaldetailsinformation.PersonalDetailsInfoActivity;
import com.bxkj.personal.ui.activity.personalmember.PersonalMemberActivity;
import com.bxkj.personal.ui.activity.postnews.PostNewsActivity;
import com.bxkj.personal.ui.activity.postnotice.PostNoticeActivity;
import com.bxkj.personal.ui.activity.postvideo.PostVideoActivity;
import com.bxkj.personal.ui.activity.privacysetting.PrivacySettingActivity;
import com.bxkj.personal.ui.activity.qaranklist.QARankListActivity;
import com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsActivity;
import com.bxkj.personal.ui.activity.questioninvite.QuestionInviteActivity;
import com.bxkj.personal.ui.activity.questioninvite.QuestionInviteModule;
import com.bxkj.personal.ui.activity.quickanswer.QuickAnswerActivity;
import com.bxkj.personal.ui.activity.recommendcompany.RecommendCompanyActivity;
import com.bxkj.personal.ui.activity.recommendjob.RecommendJobActivity;
import com.bxkj.personal.ui.activity.reportreason.ReportReasonActivity;
import com.bxkj.personal.ui.activity.resumedetails.ResumePreviewActivity;
import com.bxkj.personal.ui.activity.resumedetails.ResumePreviewModule;
import com.bxkj.personal.ui.activity.resumeopenstatesetting.ResumeOpenStateSettingActivity;
import com.bxkj.personal.ui.activity.resumesetting.ResumeSettingActivity;
import com.bxkj.personal.ui.activity.resumesetting.ResumeSettingModule;
import com.bxkj.personal.ui.activity.resumetop.ResumeTopActivity;
import com.bxkj.personal.ui.activity.resumetop.ResumeTopActivityV2;
import com.bxkj.personal.ui.activity.scanloginconfirm.ScanLoginConfirmActivity;
import com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord.DeliveryRecordFragment;
import com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord.DeliveryRecordManagementActivity;
import com.bxkj.personal.ui.activity.schoolrecruitdetails.SchoolRecruitDetailsActivity;
import com.bxkj.personal.ui.activity.searchanswer.SearchAnswerActivity;
import com.bxkj.personal.ui.activity.searchjobresult.SearchJobResultActivity;
import com.bxkj.personal.ui.activity.searchjobresult.SearchJobResultActivityV2;
import com.bxkj.personal.ui.activity.searchnews.SearchNewsActivity;
import com.bxkj.personal.ui.activity.searchnews.SearchVideoActivity;
import com.bxkj.personal.ui.activity.searchquestion.SearchQuestionActivity;
import com.bxkj.personal.ui.activity.seenmybusiness.ViewedMeCompanyAcitivity;
import com.bxkj.personal.ui.activity.seenmybusiness.ViewedMeCompanyFragment;
import com.bxkj.personal.ui.activity.selectaddress.SelectAddressActivity;
import com.bxkj.personal.ui.activity.selectaddressbymap.SelectAddressByMapActivity;
import com.bxkj.personal.ui.activity.selectarea.SelectAreaActivity;
import com.bxkj.personal.ui.activity.selectdefaultavatar.SelectDefaultAvatarActivity;
import com.bxkj.personal.ui.activity.selectdepartment.SelectDepartmentActivity;
import com.bxkj.personal.ui.activity.selectrelateschool.SelectRelateSchoolActivity;
import com.bxkj.personal.ui.activity.selectresume.SelectResumeActivity;
import com.bxkj.personal.ui.activity.service.ServiceActivity;
import com.bxkj.personal.ui.activity.setting.SettingActivity;
import com.bxkj.personal.ui.activity.shieldcompany.ShieldCompanyActivity;
import com.bxkj.personal.ui.activity.signupuser.SignUpUserActivity;
import com.bxkj.personal.ui.activity.signupuser.SignUpUserModule;
import com.bxkj.personal.ui.activity.study.StudyActivity;
import com.bxkj.personal.ui.activity.study.StudyActivityModule;
import com.bxkj.personal.ui.activity.studynewsdetails.StudyNewsDetailsActivity;
import com.bxkj.personal.ui.activity.sysrecomendsetting.SysRecommendSettingActivity;
import com.bxkj.personal.ui.activity.systemmsg.SystemMsgActivity;
import com.bxkj.personal.ui.activity.takecover.TakeCoverActivity;
import com.bxkj.personal.ui.activity.typenews.TypeNewsActivity;
import com.bxkj.personal.ui.activity.uploadattechmentresume.AttachmentResumeActivity;
import com.bxkj.personal.ui.activity.uploadavatar.UploadAvatarActivity;
import com.bxkj.personal.ui.activity.userbasicinfo.UserBasicInfoActivity;
import com.bxkj.personal.ui.activity.usersetting.UserSettingActivity;
import com.bxkj.personal.ui.activity.videodetails.VideoDetailsActivity;
import com.bxkj.personal.ui.activity.videolist.VideoListActivity;
import com.bxkj.personal.ui.activity.videosignupmsg.VideoSignUpMsgActivity;
import com.bxkj.personal.ui.activity.web.WebActivity;
import com.bxkj.personal.ui.fragment.applyrecordlist.ResumeDeliveryRecordFragment;
import com.bxkj.personal.ui.fragment.bbs.BBSFragment;
import com.bxkj.personal.ui.fragment.bbs.PostBBSNewsDialog;
import com.bxkj.personal.ui.fragment.campusrecruit.CampusRecruitFragment;
import com.bxkj.personal.ui.fragment.campusrecruit.CampusRecruitListFragment;
import com.bxkj.personal.ui.fragment.campustalk.CampusTalkFragment;
import com.bxkj.personal.ui.fragment.campustalk.RelateCampusTalkFragment;
import com.bxkj.personal.ui.fragment.homev3.GeekHomeFragmentV3;
import com.bxkj.personal.ui.fragment.joblist.LatestJobListFragment;
import com.bxkj.personal.ui.fragment.joblist.NearbyJobListFragment;
import com.bxkj.personal.ui.fragment.jobnewscontainer.GeekMainTabTwoFragment;
import com.bxkj.personal.ui.fragment.maintab.GeekMainTabFourContainerFragment;
import com.bxkj.personal.ui.fragment.maintab.MainTabFourContainerFragment;
import com.bxkj.personal.ui.fragment.maintab.MainTabOneContainerFragment;
import com.bxkj.personal.ui.fragment.maintab.MainTabTwoContainerFragment;
import com.bxkj.personal.ui.fragment.message.GeekContactFragment;
import com.bxkj.personal.ui.fragment.message.GeekContactListFragment;
import com.bxkj.personal.ui.fragment.onlinecampustalk.OnlineCampusTalkFragment;
import com.bxkj.personal.ui.fragment.parttimejoblist.PartTimeJobListFragment;
import com.bxkj.personal.ui.fragment.qa.QAActivity;
import com.bxkj.personal.ui.fragment.qa.QAActivityModule;
import com.bxkj.personal.ui.fragment.top500recruit.Top500RecruitFragment;
import com.bxkj.personal.ui.fragment.videogroup.VideoContainerFragment;
import com.bxkj.personal.weight.salaryselect.SalarySelectDialog;
import dagger.Module;
import dagger.android.ContributesAndroidInjector;

/**
 * @date 2018/3/30
 */

@Module
public abstract class PersonalUIModule {

  @ContributesAndroidInjector
  abstract IncomeAnalyzerDialogFragment incomeAnalyzerDialog();

  @ContributesAndroidInjector
  abstract CustomerSearchResultActivity customerSearchResultActivity();

  @ContributesAndroidInjector
  abstract MyProfitFragment myProfitFragment();

  @ContributesAndroidInjector
  abstract CustomerListFragment enterpriseListFragment();

  @ContributesAndroidInjector
  abstract WorkBenchFragment workBenchFragment();

  @ContributesAndroidInjector
  abstract PartTimeWorkbenchActivity partTimeWorkbenchActivity();

  @ContributesAndroidInjector
  abstract EditWorkExpActivity editWorkExpActivity();

  @ContributesAndroidInjector
  abstract FindJobOnMapActivity findJobOnMapActivity();

  @ContributesAndroidInjector
  abstract JobDetailsActivityV2 jobDetailsActivityV2();

  @ContributesAndroidInjector
  abstract PersonalMemberActivity personalMemberActivity();

  @ContributesAndroidInjector
  abstract ResumeTopActivityV2 resumeTopActivityV2();

  @ContributesAndroidInjector
  abstract ResumeDeliveryRecordFragment resumeDeliveryRecordFragment();

  @ContributesAndroidInjector
  abstract ResumeDeliveryRecordActivity resumeDeliveryRecordActivity();

  @ContributesAndroidInjector
  abstract AttachmentResumeActivity attachmentResumeActivity();

  @ContributesAndroidInjector
  abstract ResumeOpenStateSettingActivity resumeOpenStateSettingActivity();

  @ContributesAndroidInjector
  abstract VideoListActivity mVideoListActivity();

  @ContributesAndroidInjector
  abstract VideoContainerFragment videoGroupFragment();

  @ContributesAndroidInjector
  abstract RelateCampusTalkFragment mRelateCampusTalkFragment();

  @ContributesAndroidInjector
  abstract GeekMainTabTwoFragment geekMainTabTwoFragment();

  @ContributesAndroidInjector
  abstract OnlineCampusTalkDetailsActivity mOnlineCampusTalkDetailsActivity();

  @ContributesAndroidInjector
  abstract OnlineCampusTalkFragment onlineCampusTalkFragment();

  @ContributesAndroidInjector
  abstract CampusTalkDetailsActivity campusTalkDetailsActivity();

  @ContributesAndroidInjector
  abstract HotCampusRecruitListFragment mHotCampusRecruitListFragment();

  @ContributesAndroidInjector
  abstract RecruitBrochureFragment mRecruitBrochureFragment();

  @ContributesAndroidInjector
  abstract CampusRecruitDetailsActivity mCampusRecruitDetailsActivity();

  @ContributesAndroidInjector
  abstract GeekMainTabFourContainerFragment mGeekMainTabFourContainerFragment();

  @ContributesAndroidInjector
  abstract PartTimeJobContainerActivity mPartTimeJobContainerActivity();

  @ContributesAndroidInjector
  abstract SalarySelectDialog mSalarySelectDialog();

  @ContributesAndroidInjector
  abstract ViewedMeCompanyFragment mSeenMeBusinessFragment();

  @ContributesAndroidInjector
  abstract LatestJobActivity mLastJobActivity();

  @ContributesAndroidInjector
  abstract GeekInterviewDetailsActivity geekInterviewDetailsActivity();

  @ContributesAndroidInjector
  abstract GeekContactListFragment mGeekContactListFragment();

  @ContributesAndroidInjector
  abstract GeekContactFragment geekContactFragment();

  @ContributesAndroidInjector
  abstract PartTimeJobSearchResultActivity mPartTimeJobSearchResultActivity();

  @ContributesAndroidInjector
  abstract PostBBSNewsDialog mPostDialog();

  @ContributesAndroidInjector
  abstract PartTimeJobTypeListActivity mPartTimeJobTypeListActivity();

  @ContributesAndroidInjector
  abstract PartTimeJobListFragment mPartTimeJobListFragment();

  @ContributesAndroidInjector
  abstract PartTimeJobFragment partTimeJobActivity();

  @ContributesAndroidInjector
  abstract CampusTalkFragment campusTalkFragment();

  @ContributesAndroidInjector
  abstract Top500RecruitFragment top500RecruitFragment();

  @ContributesAndroidInjector
  abstract CampusRecruitListFragment campusRecruitListFragment();

  @ContributesAndroidInjector
  abstract CampusRecruitFragment campusRecruitFragment();

  @ContributesAndroidInjector
  abstract SearchJobResultActivityV2 searchJobResultActivityV2();

  @ContributesAndroidInjector
  abstract HotDiscussActivity hotDiscussActivity();

  @ContributesAndroidInjector
  abstract NearbyJobListFragment nearbyJobListFragment();

  @ContributesAndroidInjector
  abstract SelfEvaluationActivity selfEvaluationActivity();

  @ContributesAndroidInjector
  abstract MyResumeDetailsActivityV2 myResumeDetailsActivityV2();

  @ContributesAndroidInjector
  abstract JobIntentionActivity jobIntentionActivity();

  @ContributesAndroidInjector
  abstract MicroResumeInfoActivity microResumeInfoActivity();

  @ContributesAndroidInjector
  abstract HotDiscussRankActivity hotDiscussRankActivity();

  @ContributesAndroidInjector(modules = GovRecruitmentActivityModule.class)
  abstract GovRecruitmentActivity govRecruitmentActivity();

  @ContributesAndroidInjector
  abstract FineArticleDetailsActivity fineArticleDetailsActivity();

  @ContributesAndroidInjector(modules = { StudyActivityModule.class })
  abstract StudyActivity studyActivity();

  @ContributesAndroidInjector
  abstract FineArticleActivity fineArticleActivity();

  @ContributesAndroidInjector(modules = { QAActivityModule.class })
  abstract QAActivity qaActivity();

  @ContributesAndroidInjector
  abstract RecommendJobActivity recommendJobActivity();

  @ContributesAndroidInjector
  abstract RecommendCompanyActivity recommendCompanyActivity();

  @ContributesAndroidInjector
  abstract BBSFragment bbsFragment();

  @ContributesAndroidInjector
  abstract LatestJobListFragment jobListFragment();

  @ContributesAndroidInjector
  abstract GeekHomeFragmentV3 homeFragmentV3();

  @ContributesAndroidInjector
  abstract MainTabOneContainerFragment homeContainerFragment();

  @ContributesAndroidInjector
  abstract MainTabTwoContainerFragment mainTabTwoContainerFragment();

  @PerFragment
  @ContributesAndroidInjector
  abstract MainTabFourContainerFragment nearbyGroupFragment();

  @PerFragment
  @ContributesAndroidInjector
  abstract DeliveryRecordFragment deliveryRecordFragment();

  @PerActivity
  @ContributesAndroidInjector
  abstract SearchVideoActivity searchVideoActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = {
    PersonalMainActivityModule.class
  })
  abstract MainActivity personalMainActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SearchJobResultActivity searchJobResultActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SelectAreaActivity selectAreaActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SelectResumeActivity selectResumeActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SelectAddressActivity selectAddressActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract MyCollectionJobsActivity myCollectionJobsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract MyResumeListActivity myResumeListActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract CareerObjectiveActivity careerObjectiveActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract WorkExpActivity workExpActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract EduBackgroundActivity eduBackgroundActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract ProfessionalSkillActivity professionalSkillActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract LanguageSkillsActivity languageSkillsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SchoolSituationActivity schoolSituationActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract CertificateActivity certificateActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract MineSearchJobsActivity conditionSearchJobsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract FeedbackMsgDetailsActivity feedbackMsgDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = ResumeSettingModule.class)
  abstract ResumeSettingActivity resumeSettingActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = ApplicationRecordModule.class)
  abstract ApplicationRecordActivity newApplicationRecordActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = InvitationsToDeliveryModule.class)
  abstract InvitationsToDeliveryActivity invitationsToDeliveryActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract FeedbackMsgContentActivity feedbackMsgContentActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract ViewedMeCompanyAcitivity whoSewMeActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract MsgNotificationActivity msgNotificationActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract MsgNotificationContentActivity msgNotificationContentActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SystemMsgActivity systemMsgActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract CreateResumeStepTwoActivity createResumeStepTwoActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract CreateResumeStepThreeActivity createResumeStepThreeActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract CreateResumeStepFourActivity createResumeStepFourActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PersonalBasicInfoActivity personalBasicInformationActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PersonalDetailsInfoActivity personalDetailsInfoActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract MyCollectionCompanyActivity myCollectionCompanyActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SettingActivity settingActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract WebActivity webActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract ServiceActivity serviceActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract ScanLoginConfirmActivity scanLoginConfirmActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = CompanyDetailsActivityModule.class)
  abstract CompanyDetailsActivity companyDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract JobDetailsActivity newJobDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SchoolRecruitDetailsActivity schoolRecruitDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract CommentReplyActivity commentReplyActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SelectDepartmentActivity selectDepartmentActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PostNewsActivity releaseNewsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract IntegralRechargeActivity integralRechargeActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PaymentOrderActivity paymentOrderActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PaymentResultActivity paymentResultActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract UserBasicInfoActivity userBasicInfoActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract GalleryActivity galleryActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract MyFollowActivity myFollowActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract FansActivity fansActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PaidUserActivity paidUserActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract UploadAvatarActivity uploadAvatarActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SelectDefaultAvatarActivity selectDefaultAvatarActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract BindMobileActivity bindMobileActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract GzNewsDetailsActivity gzNewsDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract VideoDetailsActivity videoDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PostVideoActivity postVideoActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract TakeCoverActivity takeCoverActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PostNoticeActivity postNoticeActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SelectAddressByMapActivity selectAddressByMapActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = GzUserHomeModule.class)
  abstract GzUserHomeActivity gzUserHomeActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract QuestionDetailsActivity questionDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract AddAnswerActivity addAnswerActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract AddQuestionActivity addQuestionActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract AnswerDetailsActivity answerDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = MyActivityModule.class)
  abstract MyHistoryActivity myActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract ResumeTopActivity resumeTopActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = OrderHistoryModule.class)
  abstract OrderHistoryActivity orderHistoryActivity();

  @PerActivity
  @ContributesAndroidInjector()
  abstract SearchNewsActivity searchNewsActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = InviteUserModule.class)
  abstract InviteUserActivity inviteUserActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SearchAnswerActivity searchAnswerActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract NewMsgNotificationActivity newMsgNotificationActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract NewMsgNotificationContentActivity newMsgNotificationContentActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = QuestionInviteModule.class)
  abstract QuestionInviteActivity questionInviteActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SearchQuestionActivity searchQuestionActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract TypeNewsActivity typeNewsActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = SignUpUserModule.class)
  abstract SignUpUserActivity signUpUserActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract VideoSignUpMsgActivity videoSignUpMsgActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract QARankListActivity qaRankListActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract StudyNewsDetailsActivity studyNewsDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract QuickAnswerActivity quickAnswerActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract AreaListActivity areaListActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract ShieldCompanyActivity shieldCompanyActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract AddShieldCompanyActivity addShieldCompanyActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract UserSettingActivity userSettingActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract GeekChatContentActivity chatActivity();

  @PerActivity
  @ContributesAndroidInjector(modules = ResumePreviewModule.class)
  abstract ResumePreviewActivity resumeDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract CommonReportActivity conversationReportActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract ReportReasonActivity reportReasonActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PrivacySettingActivity privacySettingActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract PermissionManagementActivity permissionManagementActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract MomentDetailsActivity momentDetailsActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SelectRelateSchoolActivity selectRelateSchoolActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract EditRichTextActivity editRichTextActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract SysRecommendSettingActivity sysRecommendSettingActivity();

  @PerActivity
  @ContributesAndroidInjector
  abstract AboutUsV2Activity aboutUsV2Activity();

  @PerActivity
  @ContributesAndroidInjector
  abstract DeliveryRecordManagementActivity schoolRecruitDeliveryRecordActivity();
}
