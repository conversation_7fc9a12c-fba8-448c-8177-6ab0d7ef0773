package com.bxkj.personal.ui.fragment.homenews

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshLayoutViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.appendItem
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory
import com.bxkj.enterprise.ui.activity.resumedownload.ResumeDownloadNavigation
import com.bxkj.enterprise.ui.activity.selectsayhellojob.SelectSayHelloJobNavigation
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.NewsItemData
import com.bxkj.personal.data.NewsPageResponse
import com.bxkj.personal.data.QuestionItemData
import com.bxkj.personal.data.source.*
import com.bxkj.personal.enums.NewsType
import com.bxkj.personal.ui.activity.gznews.GzNewsDetailsActivity
import com.bxkj.personal.ui.activity.gzuserhome.GzUserHomeActivity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.homenews
 * @Description:
 * <AUTHOR>
 * @date 2019/12/23
 * @version V1.0
 */
class RecruitmentNewsViewModel @Inject constructor(
  private val mUserActionRepo: UserActionRepo,
  private val mNewsRepo: NewsRepo,
  private val mQuestionRepo: QuestionRepo,
  private val mCommentRepo: CommentRepo,
  private val mAccountRepo: AccountRepo,
  private val mSearchRepo: SearchRepo,
) : BaseViewModel() {

  companion object {
    //普通推荐资讯类型
    private val NORMAL_RECOMMEND_NEWS_TYPE_IDS = arrayListOf(2, 3, 4, 5)

    //置顶推荐资讯类型
    private val TOP_RECOMMEND_NEWS_TYPE_IDS = arrayListOf(1)
    private val RECOMMEND_TYPE_NEWS_IDS = arrayListOf<Int>()

    private const val NEWS_LIST_CACHE_KEY = "NEWS_LIST"
  }

  val noJobTipsEvent = LiveEvent<Unit>()
  val noGreetBalanceEvent = LiveEvent<Unit>()
  val showInviteCommand = LiveEvent<com.bxkj.enterprise.data.ResumeItemDataV2>()

  val listViewModel = RefreshListViewModel()
  private var allWDIDs: LinkedList<String>? = null
  private var allVideoIDs: List<String>? = null
  private var allCompanyIDs: List<String>? = null
  private var allIDs: List<String>? = null

  private val _recommendQAIds = mutableListOf<String>()

  private val mNewsRequestOptions = HomeNewsRequestOptions()

  fun start(bundle: Bundle?) {
    bundle?.let {
      when (val newTypeId = bundle.getInt(RecruitmentNewsFragment.EXTRA_NEWS_TYPE)) {
        NewsType.NEWS_TYPE_RECOMMEND -> {
          //取缓存资讯列表
//                    val strCacheNewsList = CacheUtil.get(NEWS_LIST_CACHE_KEY)
          val strCacheNewsList = ""
          if (!strCacheNewsList.isNullOrEmpty()) {
            val cacheNewsList = Gson().fromJson<List<NewsItemData>>(
              strCacheNewsList,
              object : TypeToken<List<NewsItemData>>() {}.type
            )
            listViewModel.addAll(cacheNewsList)
          }
          setupRecommendListViewModel(newTypeId)
          listViewModel.refresh(false)
        }

        NewsType.NEWS_TYPE_CITY, NewsType.NEWS_TYPE_DISTRICT -> {
          setupRecommendListViewModel(newTypeId)
          listViewModel.refresh()
        }

        NewsType.NEWS_TYPE_QUESTION -> {
          setupQuestionListViewModel()
        }

        NewsType.NEWS_TYPE_NORMAL -> {
          setupPublicExaminationNewsListViewModel()
          listViewModel.refresh()
        }

        else -> {
          setupNormalListViewModel(newTypeId)
          listViewModel.refresh()
        }
      }
    }
  }

  private fun setupPublicExaminationNewsListViewModel() {
    listViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mSearchRepo.searchNews(
          UserUtils.getUserSelectedCityName(),
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          listViewModel.autoAddAll(it)
        }, {
          if (it.isNoDataError) {
            listViewModel.noMoreData()
          } else {
            listViewModel.loadError()
          }
        })
      }
    }
  }

  private fun setupQuestionListViewModel() {
    listViewModel.setOnLoadDataListener { currentPage ->
      mQuestionRepo.getRecommendQuestionList(0,
        currentPage,
        CommonApiConstants.DEFAULT_PAGE_SIZE,
        object : ResultDataCallBack<List<QuestionItemData>> {
          override fun onSuccess(data: List<QuestionItemData>?) {
            listViewModel.autoAddAll(data)
          }

          override fun onError(respondThrowable: RespondThrowable) {
            if (respondThrowable.errCode == 30001) {
              listViewModel.noMoreData()
            } else {
              listViewModel.loadError()
            }
          }
        })
    }
  }

  private fun setupNormalListViewModel(newTypeId: Int) {
    listViewModel.setOnLoadDataListener { currentPage ->

      if (currentPage == 1) {
        _recommendQAIds.clear()
        if (newTypeId == NewsType.NEWS_TYPE_FOLLOW && !UserUtils.logged()) {
          listViewModel.loadFinish()
          listViewModel.showPageStatus(
            PageStatusConfigFactory.newErrorConfig().setImg(R.drawable.ic_no_login)
              .setText("您还未登录\n请登录后查看关注信息")
              .setBtnText("登录")
              .setOnButtonClickListener {
                checkLoginStateAndToLogin()
              }
          )
          return@setOnLoadDataListener
        }
      }

      mNewsRepo.getNewsList(
        getSelfUserID(),
        0,
        0,
        0,
        UserUtils.getUserSelectedCityId(),
        newTypeId,
        0,
        "",
        currentPage,
        CommonApiConstants.DEFAULT_PAGE_SIZE,
        object : ResultDataCallBack<NewsPageResponse> {
          override fun onSuccess(data: NewsPageResponse) {
            data.lastWDIDs?.let {
              _recommendQAIds.add(it)
            }
            if (data.dataList.isNullOrEmpty()) {
              listViewModel.noMoreData()
            } else {
              listViewModel.autoAddAll(data.dataList)
            }
          }

          override fun onError(respondThrowable: RespondThrowable) {
            if (respondThrowable.errCode == 30001) {
              listViewModel.noMoreData()
            } else {
              listViewModel.loadError()
            }
          }
        },
        isFollow = (newTypeId == NewsType.NEWS_TYPE_ALL),
        wdIds = _recommendQAIds.appendItem()
      )
    }
  }

  private fun setupRecommendListViewModel(newTypeId: Int) {
    listViewModel.refreshLayoutViewModel.setOnLayoutActionListener(object :
      RefreshLayoutViewModel.OnLayoutActionListenerAdapter() {
      override fun onRefresh() {
        mNewsRequestOptions.clearReuseIds()
      }
    })
    listViewModel.setOnLoadDataListener { currentPage ->
      if (!CheckUtils.isNullOrEmpty(mNewsRequestOptions.cNewIDs)) {
        mNewsRepo.getDistrictNewsList(mNewsRequestOptions.cNewIDs,
          UserUtils.getUserSelectedDistrictId(),
          "",
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE,
          object : ResultDataCallBack<List<NewsItemData>> {
            override fun onSuccess(data: List<NewsItemData>?) {
              listViewModel.addAll(data)
            }

            override fun onError(respondThrowable: RespondThrowable) {
              if (respondThrowable.errCode == 30001) {
                listViewModel.noMoreData()
              } else {
                listViewModel.loadError()
              }
            }
          })
        return@setOnLoadDataListener
      }
      if (currentPage == 1) {
        setupUserLikeInfo()
        resetRecommendNewsTypeIds()
        mNewsRequestOptions.reset()
        allVideoIDs = null
        allCompanyIDs = null
        allWDIDs = null
        allIDs = null
      } else {
        allVideoIDs?.let {
          if (currentPage <= it.size) {
            mNewsRequestOptions.videoID = it[currentPage - 1].toInt()
          } else {
            mNewsRequestOptions.videoID = 0
          }
        }
        allCompanyIDs?.let {
          if (currentPage <= it.size) {
            mNewsRequestOptions.companyID = it[currentPage - 1].toInt()
          } else {
            mNewsRequestOptions.companyID = 0
          }
        }
        allIDs?.let {
          if ((currentPage - 1) * 2 <= it.size) {
            mNewsRequestOptions.setRecommendIDs(
              it.subList(
                (currentPage - 2) * 2,
                (currentPage - 1) * 2
              )
            )
          } else {
            mNewsRequestOptions.currentIDs = CommonApiConstants.NO_TEXT
            allIDs = null
          }
        }
      }

      setupRecommendNewsId(currentPage)

      mNewsRepo.getRecommendNewsList(
        getSelfUserID(),
        CommonApiConstants.NO_ID,
        if (newTypeId == NewsType.NEWS_TYPE_RECOMMEND) 0 else UserUtils.getUserSelectedCityId(),
        0, UserUtils.getUserSelectedCityId(), "", currentPage, 4,
        object : ResultDataCallBack<NewsPageResponse> {
          override fun onSuccess(data: NewsPageResponse) {
            if (currentPage == 1) {
              if (!AuthenticationType.higherEnterpriseAuth(UserUtils.getUserRole())) {
                mNewsRequestOptions.shenfen = data.shenfen
                mNewsRequestOptions.gxId = data.gxId
              }
            }
            mNewsRequestOptions.addResumePageIndex()
            if (mNewsRequestOptions.flag != data.flag) {
              mNewsRequestOptions.switchResumeFlag(data.flag)
            }
            if (data.jobCount == 0) {
              mNewsRequestOptions.addOtherJobPageIndex()
            }
            if (allVideoIDs == null && !CheckUtils.isNullOrEmpty(data.allVideoIDs)) {
              allVideoIDs = data.allVideoIDs.split(",")
            }
            if (allCompanyIDs == null && !CheckUtils.isNullOrEmpty(data.allCompanyIDs)) {
              allCompanyIDs = data.allCompanyIDs.split(",")
            }
            if (allWDIDs == null && !CheckUtils.isNullOrEmpty(data.allWDIDs)) {
              allWDIDs = LinkedList(data.allWDIDs.split(","))
            }
            if (allIDs == null && !CheckUtils.isNullOrEmpty(data.allIDs)) {
              allIDs = data.allIDs.split(",")
            }
            mNewsRequestOptions.isCNews = data.isCNews
            mNewsRequestOptions.minID = data.minID
            mNewsRequestOptions.minIDOther = data.minIDOther

            listViewModel.autoAddAll(data.dataList)
//                        CacheUtil.put(NEWS_LIST_CACHE_KEY, Gson().toJson(data.dataList))

            if (currentPage == 1 && !CheckUtils.isNullOrEmpty(data.dataList) && data.dataList.size < 10) {
              listViewModel.loadMore()
            }
          }

          override fun onError(respondThrowable: RespondThrowable) {
            if (respondThrowable.errCode == 30001) {
              //区信息和用户已登录的情况
              if (UserUtils.logged() && newTypeId == NewsType.NEWS_TYPE_DISTRICT) {
                if (currentPage == 1) {
                  //已经带cNewIDs查过了还是没数据
                  if (mNewsRequestOptions.cNewIDs == "0") {
                    mNewsRequestOptions.clearReuseIds()
                    listViewModel.noMoreData()
                  } else {
                    //第一页没数据设置cNewIDs为0再查一次
                    mNewsRequestOptions.cNewIDs = "0"
                    listViewModel.refresh()
                  }
                } else {
                  //后面返回没数据设置allIDs再接着查
                  if (CheckUtils.isNullOrEmpty(mNewsRequestOptions.cNewIDs)) {
                    mNewsRequestOptions.reuseIDs(allIDs)
                    listViewModel.refresh()
                  } else {
                    //cNewIDs只用一次
                    mNewsRequestOptions.clearReuseIds()
                    listViewModel.noMoreData()
                  }
                }
                return
              }
              listViewModel.noMoreData()
            } else {
              listViewModel.loadError()
            }
          }
        },
        mNewsRequestOptions
      )
    }
  }

  private fun setupRecommendNewsId(currentPage: Int) {
    if (RECOMMEND_TYPE_NEWS_IDS.isNotEmpty()) {
      val recommendNewsType =
        RECOMMEND_TYPE_NEWS_IDS[(currentPage - 1) % RECOMMEND_TYPE_NEWS_IDS.size]

      mNewsRequestOptions.otherId = recommendNewsType

      allWDIDs?.let {
        //推荐资讯为问答类型 1、技能2、学历3、问答4、视频5、专业用工
        if (it.isNotEmpty() && recommendNewsType == 3) {
          mNewsRequestOptions.wdID = it.pollFirst()?.toIntOrNull()
        } else {
          mNewsRequestOptions.wdID = 0
        }
      }
    }
  }

  /**
   * 重置推荐资讯id
   */
  private fun resetRecommendNewsTypeIds() {
    RECOMMEND_TYPE_NEWS_IDS.clear()
    RECOMMEND_TYPE_NEWS_IDS.addAll(TOP_RECOMMEND_NEWS_TYPE_IDS)
    NORMAL_RECOMMEND_NEWS_TYPE_IDS.shuffle()
    RECOMMEND_TYPE_NEWS_IDS.addAll(NORMAL_RECOMMEND_NEWS_TYPE_IDS)
  }

  private fun setupUserLikeInfo() {
    if (!UserUtils.logged()) {
      mNewsRequestOptions.setUserLikeNews(mUserActionRepo.getUserLikeInfo())
    }
  }

  fun addOrRemoveLike(item: NewsItemData) {
    mCommentRepo.likeOrUnlikeTheComment(getSelfUserID(),
      CommonApiConstants.NO_ID,
      PersonalApiConstants.NEWS_TYPE_NEWS,
      item.id,
      object : ResultCallBack {
        override fun onSuccess() {
          item.addLike()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          if (respondThrowable.errCode == 10002) {
            item.removeLike()
          } else {
            showToast(respondThrowable.errMsg)
          }
        }
      })
  }

  fun addOrRemoveFollow(item: NewsItemData) {
    if (checkLoginStateAndToLogin()) {
      mAccountRepo.addOrRemoveFollow(getSelfUserID(),
        if (item.dwID == 0) PersonalApiConstants.FOLLOW_USER_TYPE else PersonalApiConstants.FOLLOW_ORG_TYPE,
        if (item.dwID == 0) item.userID else item.dwID,
        object : ResultCallBack {
          override fun onSuccess() {
            item.addFollow()
            syncNewsAuthorFollowStatus(item.dwID, item.dwID, true)
          }

          override fun onError(respondThrowable: RespondThrowable) {
            if (respondThrowable.errCode == 10002) {
              item.removeFollow()
              syncNewsAuthorFollowStatus(item.dwID, item.dwID, false)
            } else {
              showToast(respondThrowable.errMsg)
            }
          }
        })
    }
  }

  private fun syncNewsAuthorFollowStatus(
    authorId: Int,
    enterpriseId: Int,
    followStatus: Boolean
  ) {
    listViewModel.data.forEach { item ->
      if (item is NewsItemData) {
        if (authorId != 0) {
          if (item.userID == authorId) {
            item.isFollowDW = followStatus
          }
          item.job?.let {
            if (it.uid == authorId) {
              it.isFollowCompany = followStatus
            }
          }
        }
        if (enterpriseId != 0 && item.dwID == enterpriseId) {
          item.isFollowDW = followStatus
        }
      }
    }
  }

  fun addOrRemoveFollowCompany(item: com.bxkj.jrzp.user.data.JobData) {
    mAccountRepo.addOrRemoveFollow(getSelfUserID(),
      PersonalApiConstants.FOLLOW_COMPANY_TYPE,
      item.uid,
      object : ResultCallBack {
        override fun onSuccess() {
          item.addFollow()
          syncJobHRFollowStatus(item.uid, true)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          if (respondThrowable.errCode == 10002) {
            item.removeFollow()
            syncJobHRFollowStatus(item.uid, false)
          } else {
            item.isFollowCompany = item.isFollowCompany
            showToast(respondThrowable.errMsg)
          }
        }
      })
  }

  private fun syncJobHRFollowStatus(hrUserId: Int, followStatus: Boolean) {
    listViewModel.data.forEach { item ->
      if (item is NewsItemData) {
        item.job?.let {
          if (it.uid == hrUserId) {
            it.isFollowCompany = followStatus
          }
        }
      }
    }
  }

  fun loadMore() {
    listViewModel.refreshLayoutViewModel.loadMore()
  }

  fun refreshFollowData() {
    if (listViewModel.childCount == 0) {
      listViewModel.refresh(true)
    }
  }

  fun updateVideoPlayCount(item: NewsItemData) {
    mNewsRepo.updateVideoPlayCount(item.id)
  }

  fun refreshNews() {
    mNewsRequestOptions.clearReuseIds()
    listViewModel.refresh(true)
  }

  fun checkHasOnlinePosition(hasPositionCallback: () -> Unit) {
    viewModelScope.launch {
      showLoading()
      mAccountRepo.getOnlineJobList(getSelfUserID())
        .handleResult({
          if (it.isNullOrEmpty()) {
            noJobTipsEvent.call()
          } else {
            hasPositionCallback.invoke()
          }
        }, {
          if (it.isNoDataError) {
            noJobTipsEvent.call()
          } else {
            showToast(it.errMsg)
          }
        }, {
          hideLoading()
        })
    }
  }

  fun collectResume(resume: com.bxkj.enterprise.data.ResumeItemDataV2) {
    viewModelScope.launch {
      mAccountRepo.collectionResume(getSelfUserID(), resume.id)
        .handleResult({
          resume.updateCollectStatus(1)
        }, {
          if (it.errCode == 10002) {
            resume.updateCollectStatus(0)
          } else {
            showToast(it.errMsg)
          }
        })
    }
  }

  fun checkHasGreetBalance(
    resumeInfo: com.bxkj.enterprise.data.ResumeItemDataV2,
    success: () -> Unit
  ) {
    viewModelScope.launch {
      showLoading()
      mAccountRepo.getGreetBalance(getSelfUserID())
        .handleResult({
          it?.let {
            when {
              it.count > 0 -> {
                success.invoke()
              }

              it.yaoyueCount > 0 -> {
                showInviteCommand.value = resumeInfo
              }

              else -> {
                noGreetBalanceEvent.call()
              }
            }
          } ?: let {
            noGreetBalanceEvent.call()
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == RecruitmentNewsFragment.TO_NEWS_DETAILS_CODE) {
      if (resultCode == GzNewsDetailsActivity.RESULT_NEWS_STATE_CHANGE && data != null) {
        val newsItem =
          data.getParcelableExtra<NewsItemData>(GzNewsDetailsActivity.EXTRA_NEWS_ITEM)
        listViewModel.replace(newsItem)
        newsItem?.let {
          syncNewsAuthorFollowStatus(newsItem.dwID, newsItem.dwID, newsItem.isFollowDW)
        }
      }
    } else if (requestCode == RecruitmentNewsFragment.TO_NEWS_AUTHOR_HOME_CODE) {
      if (resultCode == UserHomeNavigation.RESULT_FOLLOW_STATUS_CHANGE && data != null) {
        val authorId = data.getIntExtra(UserHomeNavigation.EXTRA_QUERY_USER_ID, 0)
        val enterpriseId = data.getIntExtra(UserHomeNavigation.EXTRA_QUERY_ENTERPRISE_ID, 0)
        val followStatus =
          data.getBooleanExtra(GzUserHomeActivity.EXTRA_FOLLOW_STATUS, false)
        syncNewsAuthorFollowStatus(authorId, enterpriseId, followStatus)
      }
    } else if (requestCode == RecruitmentNewsFragment.TO_SAY_HELLO_CODE) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val resumeId = data.getIntExtra(SelectSayHelloJobNavigation.EXTRA_RESUME_ID, 0)
        syncResumeSayHelloStatus(resumeId)
      }
    } else if (requestCode == RecruitmentNewsFragment.TO_DOWNLOAD_RESUME_CODE) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val resumeId = data.getIntExtra(ResumeDownloadNavigation.EXTRA_RESUME_ID, 0)
        syncResumeDownloadStatus(resumeId)
      }
    }
  }

  private fun syncResumeDownloadStatus(resumeId: Int) {
    listViewModel.data.forEach { item ->
      if (item is NewsItemData) {
        if (item.itemType == 11 && item.resume != null) {
          val resume = item.resume
          if (resume.id == resumeId) {
            resume.updateDownloadStatus(1)
          }
        }
      }
    }
  }

  private fun syncResumeSayHelloStatus(resumeId: Int) {
    listViewModel.data.forEach { item ->
      if (item is NewsItemData) {
        if (item.itemType == 11 && item.resume != null) {
          val resume = item.resume
          if (resume.id == resumeId) {
            resume.updateSayHelloStatus(1)
          }
        }
      }
    }
  }
}