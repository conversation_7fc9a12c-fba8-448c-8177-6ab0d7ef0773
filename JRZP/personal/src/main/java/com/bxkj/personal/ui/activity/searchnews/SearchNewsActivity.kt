package com.bxkj.personal.ui.activity.searchnews

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.jzvd.Jzvd
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.data.CompanyDetailsData
import com.bxkj.personal.data.EnterpriseItemData
import com.bxkj.personal.data.JobFairItemData
import com.bxkj.personal.data.NewsItemData
import com.bxkj.personal.data.NewsTypeItemData
import com.bxkj.personal.data.QuestionItemData
import com.bxkj.personal.data.SearchHotKeyItemData
import com.bxkj.personal.data.SearchJobResultItemData
import com.bxkj.personal.data.SearchNewsResultItemData
import com.bxkj.personal.data.StudyNewsItemData
import com.bxkj.personal.data.VideoItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.databinding.PersonalActivitySearchNewsBinding
import com.bxkj.personal.enums.NewsType
import com.bxkj.personal.ui.activity.gznews.GzNewsDetailsActivity
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivity
import com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsActivity
import com.bxkj.personal.ui.activity.schoolrecruitdetails.SchoolRecruitDetailsActivity
import com.bxkj.personal.ui.activity.studynewsdetails.StudyNewsDetailsActivity
import com.bxkj.personal.ui.activity.typenews.TypeNewsActivity
import com.bxkj.personal.ui.activity.videodetails.VideoDetailsActivity
import com.bxkj.personal.ui.fragment.home.NewsTypeTabLayoutAdapter
import com.bxkj.personal.weight.SendContractInfoDialog
import net.lucode.hackware.magicindicator.FragmentContainerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.searchnews
 * @Description: 搜索新闻
 * <AUTHOR>
 * @date 2020/2/24
 * @version V1.0
 */
class SearchNewsActivity : BaseDBActivity<PersonalActivitySearchNewsBinding, SearchNewsViewModel>(),
  View.OnClickListener {

  companion object {

    const val EXTRA_KEY_PLACEHOLDER = "KEY_PLACEHOLDER"
    const val EXTRA_SEARCH_TYPE = "SEARCH_TYPE"

    const val TO_VIDEO_AUTHOR_HOME_CODE = 1
    const val TO_VIDEO_DETAILS_CODE = 2

    fun newIntent(
      context: Context,
      keyList: ArrayList<SearchHotKeyItemData>? = null,
      @NewsType.Type newsType: Int? = NewsType.NEWS_TYPE_ALL
    ): Intent {
      return Intent(context, SearchNewsActivity::class.java)
        .apply {
          putExtra(EXTRA_SEARCH_TYPE, newsType)
          putExtras(Bundle()
            .apply {
              putParcelableArrayList(EXTRA_KEY_PLACEHOLDER, keyList)
            })
        }
    }
  }

  private var mSearchNewsHistoryListAdapter: SearchNewsHistoryListAdapter? = null
  private var mSendContractInfoDialog: SendContractInfoDialog? = null

  @Inject
  lateinit var mAccountRepo: AccountRepo

  override fun getViewModelClass(): Class<SearchNewsViewModel> = SearchNewsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_search_news

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    statusBarManager.titleBar(viewBinding.llTitleBar).statusBarDarkFont(true, 0.4f).init()

    viewBinding.scrollContent.setOnTouchListener { _, event ->
      if (event.action == MotionEvent.ACTION_DOWN) {
        SystemUtil.hideSoftKeyboard(this)
      }
      return@setOnTouchListener false
    }

    subscribeViewModelEvent()
    setupHistoryListAdapter()
    setupSearchClickListener()
    setupTopRecommendList()
    setupBottomRecommendList()

    setupSendContractInfoDialog()

    setupResultListAdapter()
    viewModel.start(intent)
  }

  private fun subscribeViewModelEvent() {
    viewModel.newsTypeList.observe(this, Observer {
      setupSearchResultIndicator(it)
    })

    viewModel.openEditHistory.observe(this, Observer {
      mSearchNewsHistoryListAdapter?.showDeleteIcon(it)
    })

    viewModel.searchContent.observe(this, Observer {
      if (it.isEmpty()) {
        Jzvd.releaseAllVideos()
        viewModel.showSearchResult(false)
      }
    })

    viewModel.startSearchEvent.observe(this, Observer {
      viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).scrollToPosition(0)
      SystemUtil.hideSoftKeyboard(this)
    })

    viewModel.finishCommand.observe(this, Observer {
      finish()
    })
  }

  /**
   * 设置搜索结果type指示器
   */
  private fun setupSearchResultIndicator(newsTypeList: List<NewsTypeItemData>) {
    val switchHelper = FragmentContainerHelper(viewBinding.indicatorSearchResult)
    val commonNavigator = CommonNavigator(this).apply {
      adapter = object : NewsTypeTabLayoutAdapter(newsTypeList) {
        override fun getIndicator(context: Context?): IPagerIndicator? {
          return null
        }
      }.apply {
        setOnTabItemClickListener(object :
          OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            switchHelper.handlePageSelected(index)
            viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).scrollToPosition(0)
            viewModel.changeSearchNewsType(newsTypeList[index])
          }
        })
      }
    }
    viewBinding.indicatorSearchResult.navigator = commonNavigator
  }

  /**
   * 设置视频报名dialog
   */
  private fun setupSendContractInfoDialog() {
    mSendContractInfoDialog = SendContractInfoDialog(mAccountRepo, this)
  }

  /**
   * 新搜索结果
   */
  private fun setupResultListAdapter() {
    val multiTypeAdapter = object : MultiTypeAdapter(this) {
      var lastPosition: Int = 0
      override fun onBindViewHolder(holder: SuperViewHolder, position: Int) {
        super.onBindViewHolder(holder, position)
        if (position % 15 == 10) {
          if (lastPosition < position)
            viewModel.loadMore()
        }
        lastPosition = position
      }

      override fun getItemViewType(position: Int): Int {
        if (data[position] is SearchNewsResultItemData) {
          return (data[position] as SearchNewsResultItemData).itemType - 1;
        }
        return super.getItemViewType(position)
      }

      override fun convert(holder: SuperViewHolder, viewType: Int, item: Any, position: Int) {
        val item = mList[position]!!
        val binder: ItemViewBinder<Any> =
          typePool.getItemViewBinder(viewType) as ItemViewBinder<Any>
        if (item is SearchNewsResultItemData || item::class.java.isAssignableFrom(
            SearchNewsResultItemData::class.java
          )
        ) {
          binder.onBindViewHolder(holder, (item as SearchNewsResultItemData).infoObject, position)
        } else {
          super.convert(holder, viewType, item, position)
        }
      }
    }

    //公考资讯1
    multiTypeAdapter.register(NewsItemData::class.java,
      DefaultViewBinder<NewsItemData>(
          R.layout.personal_recycler_search_news_result_item,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
          override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
            if (v.id == R.id.tv_author_name) {
              toNewsAuthorHome(item.rzType, item.userID, item.dwID)
            } else {
              startActivity(GzNewsDetailsActivity.newIntent(this@SearchNewsActivity, item.id))
            }
          }
        }, R.id.tv_author_name)
      })

    //问答2
    multiTypeAdapter.register(
      QuestionItemData::class.java,
      DefaultViewBinder<QuestionItemData>(
          R.layout.personal_recycler_search_qa_result_item,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<QuestionItemData> {
          override fun onItemClicked(v: View, position: Int, item: QuestionItemData) {
            when (v.id) {
              R.id.iv_author_avatar, R.id.tv_author_name -> {
                UserHomeNavigation.navigate(item.userID).start()
              }
              else -> {
                startActivity(QuestionDetailsActivity.newIntent(this@SearchNewsActivity, item.id))
              }
            }
          }
        }, R.id.iv_author_avatar, R.id.tv_author_name)
      }
    )

    //视频3
    multiTypeAdapter.register(VideoItemData::class.java,
      DefaultViewBinder<VideoItemData>(
          R.layout.personal_recycler_search_video_result_item,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<VideoItemData> {
          override fun onItemClicked(v: View, position: Int, item: VideoItemData) {
            startActivity(
              VideoDetailsActivity.newIntent(
                this@SearchNewsActivity,
                item.id,
                startPlay = true
              )
            )
          }
        })
      })

    //招聘4
    multiTypeAdapter.register(SearchJobResultItemData::class.java,
      DefaultViewBinder<SearchJobResultItemData>(
          R.layout.personal_recycler_search_job_result_item,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object :
          DefaultViewBinder.OnItemClickListener<SearchJobResultItemData> {
          override fun onItemClicked(v: View, position: Int, item: SearchJobResultItemData) {
            startActivity(JobDetailsActivity.newIntent(this@SearchNewsActivity, item.id))
          }
        })
      })

    //学习5
    multiTypeAdapter.register(StudyNewsItemData::class.java,
      DefaultViewBinder<StudyNewsItemData>(
          R.layout.personal_recycler_search_study_news_result_item,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<StudyNewsItemData> {
          override fun onItemClicked(v: View, position: Int, item: StudyNewsItemData) {
            if (v.id == R.id.tv_type) {
              startActivity(
                TypeNewsActivity.newIntent(
                  this@SearchNewsActivity,
                  item.typeId,
                  item.typeName,
                  true
                )
              )
            } else {
              startActivity(StudyNewsDetailsActivity.newIntent(this@SearchNewsActivity, item.id))
            }
          }
        }, R.id.tv_type)
      })

    //企业6
    multiTypeAdapter.register(CompanyDetailsData::class.java,
      DefaultViewBinder<CompanyDetailsData>(
          R.layout.personal_recycler_search_company_result_item,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object :
          DefaultViewBinder.OnItemClickListener<CompanyDetailsData> {
          override fun onItemClicked(v: View, position: Int, item: CompanyDetailsData) {
            when (v.id) {
              R.id.tv_follow -> {
                viewModel.addOrRemoveFollowCompany(item)
              }
              R.id.ll_jobs_info -> {
                UserHomeNavigation.navigate(
                  item.userID,
                  targetTab = UserInfoNavigationData.NAVIGATION_JOB
                ).start()
              }
              else -> {
                UserHomeNavigation.navigate(item.userID).start()
              }
            }
          }
        }, R.id.tv_follow, R.id.ll_jobs_info)
      })

    //单位7
    multiTypeAdapter.register(EnterpriseItemData::class.java,
      DefaultViewBinder<EnterpriseItemData>(
          R.layout.personal_recycler_search_enterprise_result_item,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object :
          DefaultViewBinder.OnItemClickListener<EnterpriseItemData> {
          override fun onItemClicked(v: View, position: Int, item: EnterpriseItemData) {
            if (v.id == R.id.tv_follow) {
              viewModel.addOrRemoveFollowEnterprise(item)
            } else {
              toNewsAuthorHome(item.rzType, item.userID, item.dwID)
            }
          }
        }, R.id.tv_follow)
      })

    //招聘会8
    multiTypeAdapter.register(JobFairItemData::class.java,
      DefaultViewBinder<JobFairItemData>(
          R.layout.personal_recycler_search_job_fair_result_item,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<JobFairItemData> {
          override fun onItemClicked(v: View, position: Int, item: JobFairItemData) {
            startActivity(
              SchoolRecruitDetailsActivity.newIntent(
                this@SearchNewsActivity,
                0,
                item.id,
                false
              )
            )
          }
        })
      })

    val recyclerSearchResultList =
      viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    val linearLayoutManager = LinearLayoutManager(this)
    recyclerSearchResultList.layoutManager = linearLayoutManager
    recyclerSearchResultList.addItemDecoration(
      LineItemDecoration(
        ContextCompat.getDrawable(
          this,
          R.drawable.divider_f4f4f4_8
        ), LinearLayoutManager.VERTICAL
      )
    )

    viewModel.searchResultListViewModel.setAdapter(multiTypeAdapter)
  }

  private fun toNewsAuthorHome(authType: Int, userID: Int, dwID: Int) {
    when {
      AuthenticationType.isInstitutions(authType) || (dwID > 0) -> {
        UserHomeNavigation.navigate(
          userID,
          AuthenticationType.INSTITUTIONS,
          dwID
        ).start()
      }
      AuthenticationType.higherEnterpriseAuth(authType) -> {
        UserHomeNavigation.navigate(
          userID,
          authType
        ).start()
      }
      else -> {
        UserHomeNavigation.navigate(
          userID
        ).start()
      }
    }
  }

  private fun setupTopRecommendList() {
    val topRecommendAdapter = SearchRecommendListAdapter(this, viewModel)
    viewBinding.recyclerHotKey.layoutManager = GridLayoutManager(this, 2)
    viewBinding.recyclerHotKey.isNestedScrollingEnabled = false
    viewModel.setHotKeyAdapter(topRecommendAdapter)
  }

  private fun setupBottomRecommendList() {
//    val bottomAdapter = SearchRecommendListAdapter(this, viewModel)
//    dataBinding.recyclerRecommendKey.layoutManager = GridLayoutManager(this, 2)
//    dataBinding.recyclerRecommendKey.isNestedScrollingEnabled = false
//    viewModel.setBottomRecommendAdapter(bottomAdapter)
  }

  override fun onClick(v: View?) {
    v?.let {
      if (v.id == R.id.tv_clear_history) {
        ActionDialog.Builder()
          .setTitle(getString(R.string.tips))
          .setContent(getString(R.string.search_news_clear_history_tips))
          .setOnConfirmClickListener {
            viewModel.clearSearchHistory()
          }.build().show(supportFragmentManager)
      }
    }
  }

  /**
   * 设置搜索按钮点击事件
   */
  private fun setupSearchClickListener() {
    viewBinding.etSearchContent.setOnEditorActionListener { _, actionId, _ ->
      if (actionId == EditorInfo.IME_ACTION_SEARCH) {
        viewModel.startSearch()
        return@setOnEditorActionListener true
      }
      return@setOnEditorActionListener false
    }
  }

  /**
   * 设置搜索历史适配器
   */
  private fun setupHistoryListAdapter() {
    mSearchNewsHistoryListAdapter = SearchNewsHistoryListAdapter(this, viewModel)
    viewBinding.recyclerSearchHistory.layoutManager = GridLayoutManager(this, 2)
    viewBinding.recyclerSearchHistory.adapter = mSearchNewsHistoryListAdapter
    viewBinding.recyclerSearchHistory.isNestedScrollingEnabled = false
    viewModel.setHistoryAdapter(mSearchNewsHistoryListAdapter)
  }

  override fun onBackPressed() {
    if (Jzvd.backPress()) {
      return
    }
    super.onBackPressed()
  }

  override fun onPause() {
    super.onPause()
    Jzvd.releaseAllVideos()
  }

  override fun onDestroy() {
    super.onDestroy()
    Jzvd.releaseAllVideos()
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

}