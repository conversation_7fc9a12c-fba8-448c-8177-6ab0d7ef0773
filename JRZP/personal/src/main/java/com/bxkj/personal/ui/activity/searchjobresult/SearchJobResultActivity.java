package com.bxkj.personal.ui.activity.searchjobresult;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.BaseListActivity;
import com.bxkj.common.constants.AppConstants;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.data.JobTypeData;
import com.bxkj.common.enums.ChatRole;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.SPUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.util.location.LocationUtils;
import com.bxkj.common.util.recyclerutil.RecycleViewDivider;
import com.bxkj.common.util.rxbus.RxBus;
import com.bxkj.common.util.rxbus.RxMsgCode;
import com.bxkj.common.widget.DropDownMenuView;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.common.widget.dropdown.DropDownPopup;
import com.bxkj.common.widget.filterpopup.FilterGroupTitle;
import com.bxkj.common.widget.filterpopup.FilterOptionData;
import com.bxkj.common.widget.filterpopup.FilterOptionsGroup;
import com.bxkj.common.widget.filterpopup.FilterUtils;
import com.bxkj.common.widget.filterpopup.FilterView;
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory;
import com.bxkj.jrzp.support.db.SearchSqlLiteHelper;
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity;
import com.bxkj.jrzp.user.data.JobData;
import com.bxkj.personal.R;
import com.bxkj.personal.adapter.RecommendJobAdapter;
import com.bxkj.personal.mvp.contract.EducationContract;
import com.bxkj.personal.mvp.contract.GetFilterOptionsContract;
import com.bxkj.personal.mvp.contract.GetJobClassContract;
import com.bxkj.personal.mvp.contract.GetJobListContract;
import com.bxkj.personal.mvp.presenter.EducationPresenter;
import com.bxkj.personal.mvp.presenter.GetFilterOptionsPresenter;
import com.bxkj.personal.mvp.presenter.GetJobClassPresenter;
import com.bxkj.personal.mvp.presenter.GetJobListPresenter;
import com.bxkj.personal.ui.activity.conversation.GeekChatContentActivity;
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivity;
import com.bxkj.personal.ui.activity.searchjobs.SearchJobsActivity;
import com.bxkj.personal.ui.activity.selectresume.SendResumeMethod;
import com.bxkj.personal.ui.activity.selectresume.SendResumeNavigation;
import com.bxkj.personal.weight.filterareapopup.FilterAreaView;
import com.bxkj.personal.weight.filterjobclasspopup.FilterJobClassPopup;
import com.bxkj.personal.weight.filterjobclasspopup.FilterJobTypeView;
import com.zaaach.citypicker.CityPicker;
import com.zaaach.citypicker.adapter.OnPickListener;
import com.zaaach.citypicker.model.City;
import com.zaaach.citypicker.model.LocatedCity;

import io.reactivex.disposables.Disposable;
import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.searchresult
 * @Description: 搜索职位结果页
 * @TODO: TODO
 * @date 2018/4/2
 */

public class SearchJobResultActivity extends BaseListActivity
  implements GetFilterOptionsContract.View, GetJobListContract.View, GetJobClassContract.View,
  EducationContract.View {

  private static final String TAG = "SearchJobResultActivity";

  private static final String JOB_SECOND_ID = "jobSecondId";
  private static final String SEARCH_TEXT = "searchText";
  private static final String FILTER_PARAMETERS = "filterParameters";

  private static final int TO_SEARCH_JOB_CODE = 0x01;
  private static final int TO_SEND_RESUME_CODE = 0x02;
  private static final int TO_SELECT_CITY_CODE = 0x03;

  @Inject
  GetFilterOptionsPresenter mGetFilterOptionsPresenter;
  @Inject
  GetJobListPresenter mGetJobListPresenter;
  @Inject
  GetJobClassPresenter mGetJobClassPresenter;
  @Inject
  EducationPresenter mEducationPresenter;

  private TextView tvSearchText;
  private LinearLayout clJobResultFilterBar;
  private TextView tvExpectIndustry;
  private TextView tvWorkingPlace;
  private TextView tvSort;
  private TextView tvMoreRequirements;

  private List<AreaOptionsData> mAreaList, mStreetList;
  private List mMoreOptionsList;
  private FilterJobParams mFilterParameters;

  //修改*******************************************
  private DropDownPopup mDropDownPopup;
  private FilterJobTypeView mFilterJobTypeView;
  private FilterAreaView mFilterAreaView;
  private DropDownMenuView mDropDownMenuView;
  private FilterView mFilterView;

  private RecommendJobAdapter recommendJobAdapter;
  private CityPicker mCityPicker;

  private Disposable mCityChangeDisposable;

  public static Intent newIntent(Activity activity, String searchText) {
    Intent intent = new Intent(activity, SearchJobResultActivity.class);
    intent.putExtra(SEARCH_TEXT, searchText);
    return intent;
  }

  public static Intent newIntent(Activity activity, FilterJobParams filterParameters) {
    Intent intent = new Intent(activity, SearchJobResultActivity.class);
    Bundle bundle = new Bundle();
    bundle.putParcelable(FILTER_PARAMETERS, filterParameters);
    intent.putExtras(bundle);
    return intent;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mGetFilterOptionsPresenter);
    presenters.add(mGetJobListPresenter);
    presenters.add(mGetJobClassPresenter);
    presenters.add(mEducationPresenter);
    return presenters;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_search_job_result;
  }

  @Override
  protected void initPage() {
    super.initPage();

    bindView(getWindow().getDecorView());

    final Intent intent = getIntent();

    if (intent.getParcelableExtra(FILTER_PARAMETERS) != null) {
      mFilterParameters = intent.getParcelableExtra(FILTER_PARAMETERS);
      tvSearchText.setText(mFilterParameters.getTitle());
    } else {
      mFilterParameters = new FilterJobParams();
      int jobFirstId = intent.getIntExtra(JOB_SECOND_ID, CommonApiConstants.NO_DATA);
      if (jobFirstId != CommonApiConstants.NO_DATA) {
        mFilterParameters.setJobFirstTypeId(jobFirstId);
      }
      if (!CheckUtils.isNullOrEmpty(intent.getStringExtra(SEARCH_TEXT))) {
        mFilterParameters.setTitle(intent.getStringExtra(SEARCH_TEXT));
        tvSearchText.setText(intent.getStringExtra(SEARCH_TEXT));
      }
      if (UserUtils.getUserSelectedCityId() != -1) {
        mFilterParameters.setCityId(UserUtils.getUserSelectedCityId());
      }
    }

    getStatusBarManager().titleBar(findViewById(R.id.ll_title_bar)).statusBarDarkFont(true, 0.4f)
      .init();

    getLocation();
    setupCityPicker();
    setupDropDownMenu();
    subscribeCityChangeEvent();

    recommendJobAdapter = new RecommendJobAdapter(this, null, R.layout.personal_recycler_job_item,
      true);
    recommendJobAdapter.setOnItemClickListener((v, position) -> {
      final JobData jobData = recommendJobAdapter.getData().get(position);
      if (v.getId() == R.id.tv_application) {
        if (jobData != null) {
          if (jobData.isHasConversation()) {
            startActivity(GeekChatContentActivity
              .newIntent(SearchJobResultActivity.this, ChatRole.PERSONAL,
                jobData.getUid()));
          } else {
            SendResumeNavigation
              .navigate(jobData.getId(), jobData.getUid(), SendResumeMethod.CONVERSATION)
              .startForResult(SearchJobResultActivity.this, TO_SEND_RESUME_CODE);
          }
        }
      } else {
        startActivity(
          JobDetailsActivity.Companion.newIntent(SearchJobResultActivity.this,
            recommendJobAdapter.getData().get(position).getId()));
      }
    }, R.id.tv_application);
    getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
    getRecyclerView().addItemDecoration(
      new RecycleViewDivider(this, LinearLayoutManager.HORIZONTAL, DensityUtils.dp2px(this, 8),
        getMColor(R.color.common_f4f4f4)));
    getRecyclerView().setAdapter(recommendJobAdapter);

    getRefreshLayoutManager().refreshPage();
  }

  private void subscribeCityChangeEvent() {
    mCityChangeDisposable = RxBus.get().toObservable(RxBus.Message.class)
      .subscribe(message -> {
        if (message.getCode() == RxMsgCode.ACTION_SELECT_CITY_CHANGE) {
          mFilterParameters.setCityId(UserUtils.getUserSelectedCityId());
          mFilterAreaView.setCurrentCity(UserUtils.getUserSelectedCityName());
          mGetFilterOptionsPresenter.getAreaList(CommonApiConstants.GET_AREA_TYPE,
            UserUtils.getUserSelectedCityId());
          refreshPage();
        }
      });
  }

  private void setupDropDownMenu() {
    mDropDownPopup = new DropDownPopup(this, clJobResultFilterBar);
    mDropDownPopup.setOnItemExpandStatusChangeListener(
      (index, opened) -> clJobResultFilterBar.getChildAt(index).setSelected(opened));
    setupFilterAreaView();
    setupFilterJobClassView();
    setupDropDownMenuView();
    setupFilterView();
    mDropDownPopup.addContentViews(mFilterJobTypeView, mFilterAreaView, mDropDownMenuView,
      mFilterView);
  }

  private void setupFilterView() {
    //创建筛选更多条件popup
    mMoreOptionsList = new ArrayList();
    mFilterView = new FilterView.Builder(this)
      .setOnFilterConfirmListener(positionMap -> {
        mDropDownPopup.close();
        mFilterParameters.setSalaryId(getMoreOptionId(0,
          CheckUtils.checkNullReturnInt(positionMap.get(FilterOptionData.SALARY), 0)));
        mFilterParameters.setNatureOfCompanyId(getMoreOptionId(1,
          CheckUtils.checkNullReturnInt(positionMap.get(FilterOptionData.NATURE_OF_COMPANY),
            0)));
        mFilterParameters.setWorkingExpId(getMoreOptionId(2,
          CheckUtils.checkNullReturnInt(positionMap.get(FilterOptionData.WORKING_EXP), 0)));
        mFilterParameters.setDegreeId(getMoreOptionId(3,
          CheckUtils.checkNullReturnInt(positionMap.get(FilterOptionData.EDU), 0)));
        mFilterParameters.setTimeId(getMoreOptionId(4,
          CheckUtils.checkNullReturnInt(positionMap.get(FilterOptionData.PUBLISH_DATE), 0)));
        int workNatureId = getMoreOptionId(5,
          CheckUtils.checkNullReturnInt(positionMap.get(FilterOptionData.WORK_NATURE), 0));
        mFilterParameters.setNatureOfJobId(workNatureId != 0 ? (workNatureId + 7) : workNatureId);
        refreshPage();
      }).build();
    mGetFilterOptionsPresenter.getSalaryRange();
  }

  private void refreshPage() {
    getPageStatusLayout().show(PageStatusConfigFactory.newLoadingConfig());
    getRecyclerView().scrollToPosition(0);
    getRefreshLayoutManager().refreshPage();
  }

  private void setupDropDownMenuView() {
    //创建排序popup
    final String[] sorts = getResources().getStringArray(R.array.position_sort);
    mDropDownMenuView = new DropDownMenuView(this);
    mDropDownMenuView.setData(sorts);
    mDropDownMenuView.setOnItemClickListener((view, position) -> {
      mDropDownPopup.close();
      tvSort.setText(sorts[position]);
      mFilterParameters.setSort(position);
      refreshPage();
    });
  }

  private void setupFilterJobClassView() {
    mFilterJobTypeView = new FilterJobTypeView(this);
    mFilterJobTypeView.setOnItemClickListener(new FilterJobClassPopup.OnItemClickListener() {
      @Override
      public void onFirstClassItemClicked(int position) {
        int jobFirstClassId = mFilterJobTypeView.getFirstTypeData().get(position).getId();
        mFilterParameters.setJobFirstTypeId(jobFirstClassId);
        mGetJobClassPresenter.getJobClassList(CommonApiConstants.GET_JOB_SECOND_CLASS_TYPE,
          jobFirstClassId);
      }

      @Override
      public void onSecondClassClicked(int positions) {
        mDropDownPopup.close();
        mFilterParameters.setJobSecondTypeId(
          mFilterJobTypeView.getSecondTypeData().get(positions).getId());
        refreshPage();
      }
    });
    mGetJobClassPresenter.getJobClassList(CommonApiConstants.GET_JOB_FIRST_CLASS_TYPE, 0);
  }

  private ActionDialog mNoCityTipsDialog;

  private final ActivityResultLauncher<Intent> launcher = registerForActivityResult(
    new StartActivityForResult(), result -> {
      if (result.getResultCode() == CityPickerActivity.RESULT_SELECT_CITY_SUCCESS) {
        mNoCityTipsDialog.dismiss();
      }
    });

  private void setupFilterAreaView() {
    mFilterAreaView = new FilterAreaView(this);
    mFilterAreaView.setOnAreaItemClickListener(new FilterAreaView.OnAreaItemClickListener() {
      @Override
      public void onAreaItemClicked(int position) {
        AreaOptionsData selectArea = mFilterAreaView.getAreaData().get(position);
        mFilterParameters.setAreaId(selectArea.getId());
        mDropDownPopup.close();
        refreshPage();
      }

      @Override
      public void onChangeCityClicked() {
        mCityPicker.show();
      }
    });
  }

  private void getLocation() {
    new LocationUtils(getApplicationContext()
      , (LocationUtils.OnLngLatResultListener) (lat, lng) -> mFilterParameters.setLongitude(
      lat + "," + lng)).startLocation();
  }

  @Override
  protected void loadData() {
    mGetJobListPresenter.getJobList(UserUtils.getUserId(), mFilterParameters,
      getRefreshLayoutManager().getCurrentPage(),
      CommonApiConstants.DEFAULT_PAGE_SIZE);
  }

  private void setupCityPicker() {
    mCityPicker = new CityPicker()
      .setLocatedCity(
        new LocatedCity(UserUtils.getUserLocateCityName(), CommonApiConstants.NO_TEXT,
          String.valueOf(UserUtils.getUserLocateCityId())))
      .setFragmentManager(getSupportFragmentManager())
      .enableAnimation(true)
      .setAnimationStyle(R.style.RightPopupAnim)
      .setOnPickListener(new OnPickListener() {
        @Override
        public void onPick(int position, City data) {
          if (data != null) {
            UserUtils.saveUserSelectedCityInfo(Integer.parseInt(data.getCode()), data.getName());
          }
        }

        @Override
        public void onLocate() {

        }
      });
  }

  //获取筛选更多option的Id
  private int getMoreOptionId(int groupPosition, int optionPosition) {
    return ((List<FilterOptionData>) mFilterView.getOptionsGroupData(groupPosition)).get(
      optionPosition).getId();
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.iv_back) {
      finish();
    } else if (view.getId() == R.id.tv_search_text) {
      startActivityForResult(SearchJobsActivity.newIntent(this, true,
        SearchSqlLiteHelper.SEARCH_JOB_RECORD_TABLE_NAME), TO_SEARCH_JOB_CODE);
    } else if (view.getId() == R.id.tv_expect_industry) {
      mDropDownPopup.showItemAsDropDown(0);
    } else if (view.getId() == R.id.tv_work_place) {
      //未选择城市
      if (UserUtils.getUserSelectedCityId() == 0) {
        mNoCityTipsDialog = new ActionDialog.Builder()
          .setTitle("提示")
          .setContent("选择期望城市后可切换区域")
          .setOnConfirmClickListener((dialog) -> {
            launcher.launch(CityPickerActivity.newIntent(this));
          })
          .setCancelable(false).build();
        mNoCityTipsDialog.show(getSupportFragmentManager());
      } else {
        mFilterAreaView.setCurrentCity(UserUtils.getUserSelectedCityName());
        if (CheckUtils.isNullOrEmpty(mFilterAreaView.getAreaData())) {
          mGetFilterOptionsPresenter.getAreaList(CommonApiConstants.GET_AREA_TYPE,
            SPUtils.getInstance().getInt(
              AppConstants.SP_USER_CITY_ID));
        }
        mDropDownPopup.showItemAsDropDown(1);
      }
    } else if (view.getId() == R.id.tv_sort) {
      mDropDownPopup.showItemAsDropDown(2);
    } else {
      mDropDownPopup.showItemAsDropDown(3);
    }
  }

  @Override
  public void getSalaryRangeSuccess(List<FilterOptionData> filterOptionDataList) {
    mMoreOptionsList.add(new FilterGroupTitle("月薪范围"));
    mMoreOptionsList.add(new FilterOptionsGroup(FilterOptionData.SALARY, filterOptionDataList));
    mGetFilterOptionsPresenter.getNaturesOfCompany();
  }

  @Override
  public void getNaturesOfCompanySuccess(List<FilterOptionData> filterOptionDataList) {
    mMoreOptionsList.add(new FilterGroupTitle(getString(R.string.personal_company_nature)));
    mMoreOptionsList.add(
      new FilterOptionsGroup(FilterOptionData.NATURE_OF_COMPANY, filterOptionDataList, true));
    mGetFilterOptionsPresenter.getWorkingExp();
  }

  @Override
  public void getWorkingExpSuccess(List<FilterOptionData> filterOptionDataList) {
    mMoreOptionsList.add(new FilterGroupTitle(getString(R.string.personal_work_exp)));
    mMoreOptionsList.add(
      new FilterOptionsGroup(FilterOptionData.WORKING_EXP, filterOptionDataList));
    mEducationPresenter.getEducation();
  }

  @Override
  public void getEducationSuccess(List<FilterOptionData> filterOptionDataList) {
    mMoreOptionsList.add(new FilterGroupTitle(getString(R.string.personal_education)));
    mMoreOptionsList.add(new FilterOptionsGroup(FilterOptionData.EDU, filterOptionDataList));
    mMoreOptionsList.add(new FilterGroupTitle("发布时间"));
    mMoreOptionsList.add(new FilterOptionsGroup(FilterOptionData.PUBLISH_DATE,
      FilterUtils.parseFilterOptions(
        getResources().getStringArray(R.array.personal_jobs_publish_date))));
    mMoreOptionsList.add(new FilterGroupTitle(getString(R.string.personal_working_nature)));
    mMoreOptionsList.add(new FilterOptionsGroup(FilterOptionData.WORK_NATURE,
      FilterUtils.parseFilterOptions(
        getResources().getStringArray(R.array.personal_search_jobs_work_nature))));
    mFilterView.setData(mMoreOptionsList);
  }

  @Override
  public void getAreaListSuccess(int type, List<AreaOptionsData> optionsDataList) {
    if (type == CommonApiConstants.GET_AREA_TYPE) {
      mAreaList = optionsDataList;
      optionsDataList.add(0, new AreaOptionsData(0, "全" + UserUtils.getUserSelectedCityName()));
      mFilterAreaView.setAreaData(optionsDataList);
    } else {
      mStreetList = optionsDataList;
    }
  }

  @Override
  public void getJobListSuccess(List<JobData> jobDataList, boolean noMore) {
    getPageStatusLayout().hidden();
    getRefreshLayoutManager().finishRefreshOrLoadMore();
    getRefreshLayoutManager().setNoMoreData(noMore);
    if (getRefreshLayoutManager().currentFirstPage()) {
      recommendJobAdapter.reset(jobDataList);
    } else {
      recommendJobAdapter.addAll(jobDataList);
    }
  }

  @Override
  public void getJobListError() {
  }

  @Override
  public void getJobFirstClassListSuccess(List<JobTypeData> firstClassList) {
    mFilterJobTypeView.setFirstTypeData(firstClassList);
    mGetJobClassPresenter.getJobClassList(CommonApiConstants.GET_JOB_SECOND_CLASS_TYPE,
      firstClassList.get(0).getId());
  }

  @Override
  public void getJobSecondClassListSuccess(List<JobTypeData> secondClassList) {
    mFilterJobTypeView.setSecondTypeData(secondClassList);
  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == TO_SEARCH_JOB_CODE) {
      if (resultCode == RESULT_OK && data != null) {
        String resultText = data.getStringExtra(SearchJobsActivity.SEARCH_TEXT);
        getPageStatusLayout().show(PageStatusConfigFactory.newLoadingConfig());
        tvSearchText.setText(resultText);
        mFilterParameters.setTitle(resultText);
        mFilterAreaView.setCurrentCity(UserUtils.getUserSelectedCityName());
        mGetFilterOptionsPresenter.getAreaList(CommonApiConstants.GET_AREA_TYPE,
          UserUtils.getUserSelectedCityId());
        mFilterParameters.setCityId(UserUtils.getUserSelectedCityId());
        refreshPage();
      }
    } else if (requestCode == TO_SEND_RESUME_CODE) {
      if (resultCode == RESULT_OK && data != null) {
        final int jobId = data.getIntExtra(SendResumeNavigation.EXTRA_JOB_ID, 0);
        final int companyUserId = data.getIntExtra(SendResumeNavigation.EXTRA_COMPANY_USER_ID, 0);
        syncSayHelloStatus(jobId, companyUserId);
      }
    }
  }

  /**
   * 同步打招呼状态
   */
  private void syncSayHelloStatus(int jobId, int companyUserId) {
    for (int i = 0; i < recommendJobAdapter.getData().size(); i++) {
      final JobData jobData = recommendJobAdapter.getData().get(i);
      if (jobData.getId() == jobId && jobData.getUid() == companyUserId) {
        jobData.setHasConversation(true);
        recommendJobAdapter.notifyItemChanged(i);
      }
    }
  }

  @Override
  protected void onDestroy() {
    if (mCityChangeDisposable != null && !mCityChangeDisposable.isDisposed()) {
      mCityChangeDisposable.dispose();
    }
    super.onDestroy();
  }

  @Override
  public void finish() {
    if (mDropDownPopup.hasOpenMenu()) {
      mDropDownPopup.close();
    } else {
      super.finish();
    }
  }

  private void bindView(View bindSource) {
    tvSearchText = bindSource.findViewById(R.id.tv_search_text);
    clJobResultFilterBar = bindSource.findViewById(R.id.cl_job_result_filter_bar);
    tvExpectIndustry = bindSource.findViewById(R.id.tv_expect_industry);
    tvWorkingPlace = bindSource.findViewById(R.id.tv_work_place);
    tvSort = bindSource.findViewById(R.id.tv_sort);
    tvMoreRequirements = bindSource.findViewById(R.id.tv_more_requirements);
    bindSource.findViewById(R.id.iv_back).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_search_text).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_expect_industry).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_work_place).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_sort).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_more_requirements).setOnClickListener(v -> onViewClicked(v));
  }
}
