package com.bxkj.personal.ui.activity.mycollectionjobs;

import com.bxkj.common.mvp.mvp.BaseHasListView;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.personal.data.MyCollectionJobData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.mycollectionjobs
 * @Description: MyCollectionJobs
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface MyCollectionJobsContract {
    interface View extends BaseHasListView {
        void getUserCollectionJobsSuccess(List<MyCollectionJobData> userCollectionJobsList);

        void uncollectionJobSuccess(int position);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getUserCollectionJobs(int userId, int pageIndex, int pageSize);

        public abstract void uncollectionJob(int userId, int jobId, int itemPosition);
    }
}
