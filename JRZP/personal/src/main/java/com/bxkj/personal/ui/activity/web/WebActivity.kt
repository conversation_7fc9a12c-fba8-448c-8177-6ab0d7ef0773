package com.bxkj.personal.ui.activity.web

import android.app.Activity
import android.content.Intent
import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebView
import com.therouter.router.Route
import com.alipay.sdk.app.PayTask
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.util.web.BaseJSInterface.OnJSCallListener
import com.bxkj.common.widget.YUIWebView.OnUrlLoadingListener
import com.bxkj.enterprise.ui.activity.beanusagerecord.BeanUsageRecordNavigation
import com.bxkj.enterprise.ui.activity.buyfunctionpackages.BuyFunctionPackageNavigation
import com.bxkj.enterprise.ui.activity.membercenter.MemberCenterNavigation
import com.bxkj.jrzp.support.feature.ui.shareposter.SharePosterDialogActivity
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R
import com.bxkj.personal.R.layout
import com.bxkj.personal.databinding.ActivityWebBinding
import com.bxkj.personal.weight.pagemoreoptions.PageMoreOptionsPopup
import com.bxkj.share.ShareOpID
import com.bxkj.share.ShareOption
import com.bxkj.share.ui.StandardShareDialog
import com.elvishew.xlog.XLog
import com.google.gson.JsonObject

/**
 * @Description:
 * @TODO: TODO
 * @date 2019/1/22
 */
const val TO_LOGIN_CODE = 99

@Route(path = RouterNavigation.WebActivity.URL)
class WebActivity : BaseDBActivity<ActivityWebBinding, WebViewModel>() {

  private var mRealUrl: String = CommonApiConstants.NO_TEXT
  private var mSharePopup: PageMoreOptionsPopup? = null

  private var tempWXOrderId: String = ""

  override fun getLayoutId(): Int {
    return layout.activity_web
  }

  override fun getViewModelClass(): Class<WebViewModel> {
    return WebViewModel::class.java
  }

  override fun initPage(savedInstanceState: Bundle?) {
    if (intent.getBooleanExtra(WebNavigation.EXTRA_FULL_SCREEN, false)) {
      viewBinding.titleBar.visibility = View.GONE
      statusBarManager.titleBar(viewBinding.flContent).init()
    } else {
      val pageTitle = intent.getStringExtra(WebNavigation.EXTRA_PAGE_TITLE)
      pageTitle?.let {
        viewBinding.titleBar.setTitle(pageTitle)
      }
    }

    viewBinding.titleBar.setLeftOptionClickListener {
      if (viewBinding.webContent.canGoBack()) {
        viewBinding.webContent.goBack()
      } else {
        finish()
      }
    }

    subscribeRxBusMsg()
    subscribeViewModelEvent()

    viewBinding.webContent.setOnUrlLoadingListener(object : OnUrlLoadingListener {
      override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
        if (url.startsWith("weixin")) {
          SystemUtil.openSchemeUrl(this@WebActivity, url, 66)
          return true
        }

        val payTask = PayTask(this@WebActivity)
        val isIntercepted = payTask.payInterceptorWithUrl(url, true) { result ->
          result?.let {
            val returnUrl = it.returnUrl
            if (!CheckUtils.isNullOrEmpty(returnUrl)) {
              runOnUiThread {
                viewBinding.webContent.markClearHistory()
                view.loadUrl(returnUrl)
              }
            } else {
              runOnUiThread {
                viewBinding.webContent.markClearHistory()
                viewBinding.webContent.reload()
              }
            }
          }
        }
        if (!isIntercepted) {
          view.loadUrl(url)
        }
        return true
      }
    })

    viewBinding.webContent.webChromeClient = object : WebChromeClient() {
      override fun onProgressChanged(view: WebView, newProgress: Int) {
        if (newProgress == 100) {
          viewBinding.progress.visibility = View.GONE
        } else {
          viewBinding.progress.visibility = View.VISIBLE
          viewBinding.progress.progress = newProgress
        }
      }

      override fun onReceivedTitle(view: WebView?, title: String?) {
        if (intent.getStringExtra(WebNavigation.EXTRA_PAGE_TITLE).isNullOrEmpty()) {
          title?.let {
            if (title != "weixin") {
              viewBinding.titleBar.setTitle(it)
            }
            if (it == "我的权益") {
              viewBinding.titleBar.setBackgroundColor(getResColor(R.color.black))
              viewBinding.titleBar.setTitleColor(getResColor(R.color.white))
              viewBinding.titleBar.setLeftImage(R.drawable.common_ic_white_back)
              viewBinding.titleBar.setRightText("使用明细")
              viewBinding.titleBar.setRightTextBg(null)
              viewBinding.titleBar.setRightTextColor(getResColor(R.color.cl_ff7405))
              viewBinding.titleBar.setRightOptionClickListener {
                BeanUsageRecordNavigation.create().start()
              }
            } else {
              viewBinding.titleBar.setBackgroundColor(getResColor(R.color.white))
              viewBinding.titleBar.setTitleColor(getResColor(R.color.cl_333333))
              viewBinding.titleBar.setLeftImage(R.drawable.common_ic_back)
              viewBinding.titleBar.setRightText("")
              viewBinding.titleBar.setRightTextBg(null)
            }
          }
        }
      }
    }

    viewBinding.webContent.addJavascriptInterface(WebJSInterface(this, { url ->
      mRealUrl = url
    }, { infoId, shareUrl ->
      viewModel.setupShareInfo(infoId, shareUrl)
    }, mOnJSCallListener = object : OnJSCallListener {
      override fun onJSCall(method: String?, params: JsonObject?) {
        XLog.d("onJSCall: method=$method, params=$params")
        handleJSCall(method, params)
      }
    }), AppConstants.JS_BRIDGE_NAME)

    val url = intent.getStringExtra(WebNavigation.EXTRA_URL).getOrDefault()
    viewBinding.webContent.loadUrl(url)
  }

  private fun handleJSCall(method: String?, params: JsonObject?) {
    method?.let {
      when (it) {
        "shareToWxMiniProgram" -> {
          params?.get("path")?.let { jsonPath ->
            jsonPath.asString?.let { path ->
              StandardShareDialog.Builder()
                .setShareMiniProgramPath(path)
                .setShareTitle("海量精品职位\n快来注册找工作吧！")
                .setShareBitmap(
                  BitmapFactory.decodeResource(
                    resources,
                    R.drawable.personal_ic_app_logo
                  )
                )
                .setShareOptions(
                  arrayListOf(
                    ShareOption.get(
                      R.drawable.ic_page_options_share_to_wechat,
                      "微信",
                      ShareOpID.SHARE_WECHAT
                    )
                  )
                )
                .build().show(supportFragmentManager)
            }
          }
        }

        "showInvitePoster" -> {
          viewModel.getInvitationCode()
        }

        "saveWXOrderId" -> {
          params?.let {
            val wxOrderId: String? = params.get("orderId").asString
            if (!wxOrderId.isNullOrBlank()) {
              tempWXOrderId = wxOrderId
            }
          }
        }

        "toBeanExchangePage" -> {
          params?.let {
            val tag = params.get("tag").asInt
            BuyFunctionPackageNavigation.create(tag).start()
          }
        }

        "toMemberCenterPage" -> {
          MemberCenterNavigation.create().start()
        }

        else -> {
        }
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.getInvitationCodeSuccessEvent.observe(this, EventObserver {
      startActivity(
        SharePosterDialogActivity.newIntent(
          this,
          it,
          "求职招聘\n上今日招聘",
          "扫码注册快人一步"
        )
      )
    })

    viewModel.showShareCommand.observe(this) {
      mSharePopup = PageMoreOptionsPopup(
        this, it.title, it.content, it.shareUrl, showCollection = false
      ).apply {
        showBottom()
      }
    }
  }

  private fun subscribeRxBusMsg() {
    addDisposable(RxBus.get().toObservable(RxBus.Message::class.java).subscribe { it ->
      if (it.code == RxMsgCode.ACTION_LOGIN_SUCCESS) {
        if (mRealUrl.isNotEmpty()) {
          viewBinding.webContent.loadUrl(
            "http://wangxiao.jrzp.com/web/appLogin.aspx?para=${UserUtils.getUserWebToken()}&url=$mRealUrl"
          )
        } else {
          viewBinding.webContent.reload()
        }
      } else if (it.code == RxMsgCode.ACTION_SHARE_BACK) {
        mSharePopup?.dismiss()
      } else if (it.code == RxMsgCode.BUY_BEAN_PACKAGE_SUCCESS) {
        viewBinding.webContent.reload()
      }
    })
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_LOGIN_CODE) {
      if (viewBinding.webContent.url == "http://wangxiao.jrzp.com/login.html") {
        viewBinding.webContent.goBack()
      }
    } else if (requestCode == TO_USER_HOME_CODE) {
      if (resultCode == UserHomeNavigation.RESULT_FOLLOW_STATUS_CHANGE && data != null) {
        val userID = data.getIntExtra(UserHomeNavigation.EXTRA_QUERY_USER_ID, 0)
        val followStatus = data.getBooleanExtra(UserHomeNavigation.EXTRA_FOLLOW_STATUS, false)
        viewBinding.webContent.execJS(
          "javascript:Update(${userID},${(if (followStatus) 1 else 0)})", null
        )
      }
    } else if (requestCode == 66) {
      viewBinding.webContent.markClearHistory()
      viewBinding.webContent.loadUrl("https://jrzpapi2.jdzj.com/AppPay/payResult.aspx?orderNo=${tempWXOrderId}")
      tempWXOrderId = ""
    }
  }

  override fun onPause() {
    super.onPause()
    viewBinding.webContent.onPause()
  }

  override fun onResume() {
    super.onResume()
    viewBinding.webContent.onResume()
  }

  override fun onDestroy() {
    super.onDestroy()
    viewBinding.webContent.destroy()
  }

  override fun onBackPressed() {
    if (viewBinding.webContent.canGoBack()) {
      viewBinding.webContent.goBack()
    } else {
      super.onBackPressed()
    }
  }

  companion object {

    const val TO_USER_HOME_CODE = 1

    fun newIntent(
      activity: Activity?,
      pageTitle: String? = CommonApiConstants.NO_TEXT,
      webUrl: String? = CommonApiConstants.NO_TEXT,
      fullScreen: Boolean = false
    ): Intent {
      val intent = Intent(activity, WebActivity::class.java)
      intent.putExtra(WebNavigation.EXTRA_PAGE_TITLE, pageTitle)
      intent.putExtra(WebNavigation.EXTRA_URL, webUrl)
      intent.putExtra(WebNavigation.EXTRA_FULL_SCREEN, fullScreen)
      return intent
    }
  }
}