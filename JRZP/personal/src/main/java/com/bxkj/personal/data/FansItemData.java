package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.jrzp.user.ui.enterpriseauth.AuthenticationType;
import com.bxkj.personal.R;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.data
 * @Description: 粉丝
 * @date 2020/1/15
 */
public class FansItemData extends BaseObservable {

  /**
   * id : 121
   * nickName :
   * sex : 0
   * photo : http://img.jrzp.com/images_server/comm/nan.png
   * actionCount : 0
   * isFollowed : false
   */

  private int id;
  private String nickName;
  private int sex;
  private String photo;
  private int actionCount;
  private boolean isFollowed;
  private int rzType;
  private String tradeName;
  private int relCount;
  private String cityName;
  private int level;

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getNickName() {
    return nickName;
  }

  public void setNickName(String nickName) {
    this.nickName = nickName;
  }

  public int getSex() {
    return sex;
  }

  public void setSex(int sex) {
    this.sex = sex;
  }

  public String getPhoto() {
    return photo;
  }

  public void setPhoto(String photo) {
    this.photo = photo;
  }

  public int getActionCount() {
    return actionCount;
  }

  public void setActionCount(int actionCount) {
    this.actionCount = actionCount;
  }

  @Bindable
  public boolean isIsFollowed() {
    return isFollowed;
  }

  public void setIsFollowed(boolean isFollowed) {
    this.isFollowed = isFollowed;
    notifyPropertyChanged(com.bxkj.personal.BR.isFollowed);
  }

  public void addFollow() {
    setIsFollowed(true);
  }

  public void removeFollow() {
    setIsFollowed(false);
  }

  public boolean isSelf() {
    return UserUtils.logged() && UserUtils.getUserId() == id;
  }

  public String getUserDesc() {
    StringBuilder userDesc = new StringBuilder();
    if (isAuthEnterprise()) {
      userDesc.append(tradeName);
      userDesc.append(" | ");
      userDesc.append("在招职位:");
      userDesc.append(relCount);
    } else if (isAuthSchool()) {
      userDesc.append(cityName);
      if (!CheckUtils.isNullOrEmpty(tradeName)) {
        userDesc.append(" | ");
        userDesc.append(tradeName);
      }
    } else {
      userDesc.append(actionCount);
      userDesc.append("条动态");
    }
    return userDesc.toString();
  }

  public boolean  isAuthEnterprise() {
    return rzType == 2 || rzType == 3;
  }

  public boolean isAuthSchool() {
    return rzType == 4;
  }

  public boolean higherEnterpriseAuth() {
    return AuthenticationType.higherEnterpriseAuth(rzType);
  }

  public int getMemberLevelIcon() {
    switch (level) {
      case 1101: {
        return R.drawable.ic_member_bule;
      }
      case 2101: {
        return R.drawable.ic_member_gold;
      }
      case 3101: {
        return R.drawable.ic_member_diamond;
      }
      default: {
        return -1;
      }
    }
  }

  public boolean isVip() {
    return level > 101;
  }
}
