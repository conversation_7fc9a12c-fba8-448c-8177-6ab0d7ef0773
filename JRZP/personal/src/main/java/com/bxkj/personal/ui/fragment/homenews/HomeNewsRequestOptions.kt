package com.bxkj.personal.ui.fragment.homenews

import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.UserUtils
import com.bxkj.jrzp.support.db.entry.UserActionRecord
import java.lang.StringBuilder

/**
 * <AUTHOR>
 * @version V1.0
 * @Description:
 * @date 2020/3/3
 */
data class HomeNewsRequestOptions(
  var minID: Int? = CommonApiConstants.NO_ID,
  var minIDOther: Int? = CommonApiConstants.NO_ID,
  var wdID: Int? = CommonApiConstants.NO_ID,
  var videoID: Int? = CommonApiConstants.NO_ID,
  var companyID: Int? = CommonApiConstants.NO_ID,
  var currentIDs: String? = CommonApiConstants.NO_TEXT,
  var cNewIDs: String = CommonApiConstants.NO_TEXT,
  var uaTypes: String? = CommonApiConstants.NO_TEXT,
  var uaKeys: String? = CommonApiConstants.NO_TEXT,
  var isCNews: Boolean = false,
  var jobPageIndex: Int = 0,
  var otherId: Int = 1,
  var shenfen: Int = UserUtils.getUserRole(),
  var gxId: Int = 0,
  var resPageIndex: Int = 1,
  var flag: Int = 0,
  var resProvince: Int = UserUtils.getUserProvinceId(),
  var resCity: Int = UserUtils.getUserSelectedCityId()
) {

  /**
   * 设置推荐id
   */
  fun setRecommendIDs(ids: List<String>) {
    val idBuilder = StringBuilder()
    ids.forEach {
      idBuilder.append(it).append(",")
    }
    currentIDs = idBuilder.substring(0, idBuilder.length - 1)
  }

  /**
   * 复用id
   */
  fun reuseIDs(allIDs: List<String>?) {
    allIDs?.let {
      val reuseIdsBuilder = StringBuilder()
      it.forEach { itemId ->
        reuseIdsBuilder.append(itemId).append(",")
      }
      cNewIDs = reuseIdsBuilder.substring(0, reuseIdsBuilder.length - 1)
    }
  }

  /**
   * 设置用户喜欢的资讯
   */
  fun setUserLikeNews(list: List<UserActionRecord>?) {
    list?.let {
      val userLikeTypesBuilder = StringBuilder()
      val userLikeKeyBuilder = StringBuilder()
      list.forEach {
        if (it.type != 0) {
          userLikeTypesBuilder.append(it.type).append(",")
        } else {
          userLikeKeyBuilder.append(it.key).append(",")
        }
      }
      if (userLikeTypesBuilder.isNotEmpty()) {
        uaTypes = userLikeTypesBuilder.substring(0, userLikeTypesBuilder.length - 1)
      }
      if (userLikeKeyBuilder.isNotEmpty()) {
        uaKeys = userLikeKeyBuilder.substring(0, userLikeKeyBuilder.length - 1)
      }
    }
  }

  /**
   * 清除推荐列表参数
   */
  fun reset() {
    minID = CommonApiConstants.NO_ID
    minIDOther = CommonApiConstants.NO_ID
    wdID = CommonApiConstants.NO_ID
    videoID = CommonApiConstants.NO_ID
    companyID = CommonApiConstants.NO_ID
    currentIDs = CommonApiConstants.NO_TEXT
    isCNews = false
    otherId = 1
    shenfen = UserUtils.getUserRole()
    gxId = 0
    resPageIndex = 1
    flag = 0
    resProvince=UserUtils.getUserProvinceId()
    resCity=UserUtils.getUserSelectedCityId()
    resetOtherJobPageIndex()
  }

  /**
   * 清除重用id
   */
  fun clearReuseIds() {
    cNewIDs = CommonApiConstants.NO_TEXT
  }

  /**
   * 重置其他职位页码
   */
  private fun resetOtherJobPageIndex() {
    jobPageIndex = 0
  }

  /**
   * 增加其他职位页码
   */
  fun addOtherJobPageIndex() {
    jobPageIndex += 1
  }

  fun switchResumeFlag(flag: Int) {
    this.flag = flag
    resPageIndex = 1
  }

  fun addResumePageIndex() {
    resPageIndex += 1
  }

}
